#!/usr/bin/env python3
"""
Debug script to examine the cam-playa.js script.
"""

import requests
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

def examine_cam_script():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # Try to fetch the cam script
    cam_script_url = "https://meteo365.es/livecams/jscams/cam-playa.js"
    
    try:
        print(f"Fetching: {cam_script_url}")
        response = session.get(cam_script_url, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            script_content = response.text
            print(f"Script length: {len(script_content)} characters")
            
            # Look for image URLs
            img_urls = re.findall(r'["\']([^"\']*\.(?:jpg|jpeg|png|gif|webp))["\']', script_content)
            if img_urls:
                print(f"\nImage URLs found: {img_urls}")
            
            # Look for blob URLs
            blob_urls = re.findall(r'blob:[^"\'\s]+', script_content)
            if blob_urls:
                print(f"\nBlob URLs found: {blob_urls}")
            
            # Look for UUID patterns
            uuid_patterns = re.findall(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', script_content)
            if uuid_patterns:
                print(f"\nUUIDs found: {uuid_patterns}")
            
            # Look for fetch or XMLHttpRequest calls
            fetch_patterns = re.findall(r'fetch\s*\(\s*["\']([^"\']+)["\']', script_content)
            if fetch_patterns:
                print(f"\nFetch URLs: {fetch_patterns}")
            
            xhr_patterns = re.findall(r'\.open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']', script_content)
            if xhr_patterns:
                print(f"\nXHR URLs: {xhr_patterns}")
            
            # Look for any URL patterns
            url_patterns = re.findall(r'https?://[^\s"\'<>]+', script_content)
            if url_patterns:
                print(f"\nAll URLs found: {url_patterns}")
            
            # Look for function definitions that might be relevant
            function_patterns = re.findall(r'function\s+(\w+)\s*\([^)]*\)', script_content)
            if function_patterns:
                print(f"\nFunctions found: {function_patterns}")
            
            # Look for variable assignments that might contain URLs
            var_patterns = re.findall(r'var\s+\w+\s*=\s*["\']([^"\']+)["\']', script_content)
            if var_patterns:
                print(f"\nVariable assignments: {var_patterns}")
            
            # Print the first 1000 characters to see the structure
            print(f"\nFirst 1000 characters of script:")
            print(script_content[:1000])
            
            # Save the full script for manual inspection
            with open('cam-playa.js', 'w') as f:
                f.write(script_content)
            print(f"\nSaved full script to cam-playa.js")
            
        else:
            print(f"Failed to fetch script: {response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    # Also try some variations
    variations = [
        "https://meteo365.es/livecams/jscams/cam-playa.js?t=123",
        "https://meteo365.es/jscams/cam-playa.js",
        "https://meteo365.es/jscams/cam-playa.js?t=123",
    ]
    
    for url in variations:
        try:
            print(f"\nTrying variation: {url}")
            response = session.get(url, timeout=10)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print(f"Content length: {len(response.text)}")
                # Look for image URLs in this variation
                img_urls = re.findall(r'["\']([^"\']*\.(?:jpg|jpeg|png|gif|webp))["\']', response.text)
                if img_urls:
                    print(f"Image URLs: {img_urls}")
        except Exception as e:
            print(f"Error with {url}: {e}")

if __name__ == "__main__":
    examine_cam_script()
