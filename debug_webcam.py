#!/usr/bin/env python3
"""
Debug script to find the actual webcam image URL.
"""

import requests
from bs4 import BeautifulSoup
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
METEO365_URL = "https://meteo365.es/livecams/malaga.php"

def find_webcam_image():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        response = session.get(METEO365_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print("=== LOOKING FOR WEBCAM IMAGES ===")
        
        # Look for all images
        images = soup.find_all('img')
        print(f"Found {len(images)} images total")
        
        # Look for images that might be the webcam
        webcam_candidates = []
        for img in images:
            src = img.get('src', '')
            alt = img.get('alt', '')
            
            # Check if this looks like a webcam image
            if any(keyword in src.lower() for keyword in ['cam', 'live', 'webcam', 'malaga']):
                webcam_candidates.append({
                    'src': src,
                    'alt': alt,
                    'full_url': src if src.startswith('http') else f"https://meteo365.es{src}" if src.startswith('/') else f"https://meteo365.es/livecams/{src}"
                })
        
        print(f"\nFound {len(webcam_candidates)} webcam candidates:")
        for i, candidate in enumerate(webcam_candidates):
            print(f"  {i+1}. {candidate}")
        
        # Look for JavaScript that might load the webcam image
        print("\n=== LOOKING FOR JAVASCRIPT WEBCAM LOADING ===")
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                text = script.string
                # Look for image URLs in JavaScript
                image_urls = re.findall(r'["\']([^"\']*\.(?:jpg|jpeg|png|gif|webp))["\']', text, re.IGNORECASE)
                if image_urls:
                    print(f"Found image URLs in script: {image_urls}")
                
                # Look for webcam-related variables
                if any(keyword in text.lower() for keyword in ['cam', 'webcam', 'live', 'image']):
                    lines = text.split('\n')
                    for line in lines:
                        if any(keyword in line.lower() for keyword in ['cam', 'webcam', 'live', 'image']):
                            print(f"Webcam-related JS line: {line.strip()}")
        
        # Try to find the actual live webcam image
        print("\n=== TESTING WEBCAM CANDIDATES ===")
        for i, candidate in enumerate(webcam_candidates):
            try:
                print(f"\nTesting candidate {i+1}: {candidate['full_url']}")
                img_response = session.get(candidate['full_url'], timeout=10)
                print(f"  Status: {img_response.status_code}")
                print(f"  Content-Type: {img_response.headers.get('content-type', 'unknown')}")
                print(f"  Content-Length: {len(img_response.content)} bytes")
                
                # Check if it's actually an image
                if img_response.headers.get('content-type', '').startswith('image/'):
                    print(f"  ✓ Valid image found!")
                    
                    # Save a sample for inspection
                    filename = f"webcam_candidate_{i+1}.jpg"
                    with open(filename, 'wb') as f:
                        f.write(img_response.content)
                    print(f"  Saved sample to {filename}")
                
            except Exception as e:
                print(f"  Error testing candidate: {e}")
        
        # Look for common webcam patterns
        print("\n=== LOOKING FOR COMMON WEBCAM PATTERNS ===")
        
        # Check for common webcam URLs
        common_patterns = [
            "https://meteo365.es/livecams/malaga.jpg",
            "https://meteo365.es/livecams/images/malaga.jpg",
            "https://meteo365.es/cams/malaga.jpg",
            "https://cam.meteo365.es/malaga.jpg",
            "https://cam.meteo365.es/malaga/current.jpg",
        ]
        
        for pattern in common_patterns:
            try:
                print(f"\nTesting pattern: {pattern}")
                img_response = session.get(pattern, timeout=10)
                print(f"  Status: {img_response.status_code}")
                if img_response.status_code == 200:
                    print(f"  Content-Type: {img_response.headers.get('content-type', 'unknown')}")
                    print(f"  Content-Length: {len(img_response.content)} bytes")
                    if img_response.headers.get('content-type', '').startswith('image/'):
                        print(f"  ✓ Found working webcam URL!")
                        filename = f"webcam_pattern_{pattern.split('/')[-1]}"
                        with open(filename, 'wb') as f:
                            f.write(img_response.content)
                        print(f"  Saved to {filename}")
            except Exception as e:
                print(f"  Error: {e}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    find_webcam_image()
