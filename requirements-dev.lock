# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
anyio==4.9.0
    # via httpx
    # via jupyter-server
appnope==0.1.4
    # via ipykernel
argon2-cffi==25.1.0
    # via jupyter-server
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
arrow==1.3.0
    # via isoduration
asttokens==3.0.0
    # via stack-data
async-lru==2.0.5
    # via jupyterlab
attrs==25.3.0
    # via jsonschema
    # via referencing
babel==2.17.0
    # via jupyterlab-server
beautifulsoup4==4.13.4
    # via nbconvert
    # via surf-malaga
bleach==6.2.0
    # via nbconvert
certifi==2025.7.9
    # via httpcore
    # via httpx
    # via requests
cffi==1.17.1
    # via argon2-cffi-bindings
    # via cryptography
charset-normalizer==3.4.2
    # via pdfminer-six
    # via requests
comm==0.2.2
    # via ipykernel
    # via ipywidgets
contourpy==1.3.2
    # via matplotlib
cryptography==45.0.5
    # via pdfminer-six
cycler==0.12.1
    # via matplotlib
debugpy==1.8.14
    # via ipykernel
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via nbconvert
executing==2.2.0
    # via stack-data
fastjsonschema==2.21.1
    # via nbformat
fonttools==4.58.5
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via jupyterlab
idna==3.10
    # via anyio
    # via httpx
    # via jsonschema
    # via requests
ipykernel==6.29.5
    # via jupyter
    # via jupyter-console
    # via jupyterlab
ipython==9.4.0
    # via ipykernel
    # via ipywidgets
    # via jupyter-console
ipython-genutils==0.2.0
    # via jupyter-contrib-nbextensions
ipython-pygments-lexers==1.1.1
    # via ipython
ipywidgets==8.1.7
    # via jupyter
isoduration==20.11.0
    # via jsonschema
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via jupyter-server
    # via jupyterlab
    # via jupyterlab-server
    # via nbconvert
joblib==1.5.1
    # via scikit-learn
json5==0.12.0
    # via jupyterlab-server
jsonpointer==3.0.0
    # via jsonschema
jsonschema==4.24.0
    # via jupyter-events
    # via jupyterlab-server
    # via nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter==1.1.1
    # via surf-malaga
jupyter-client==8.6.3
    # via ipykernel
    # via jupyter-console
    # via jupyter-server
    # via nbclient
jupyter-console==6.6.3
    # via jupyter
jupyter-contrib-core==0.4.2
    # via jupyter-contrib-nbextensions
    # via jupyter-nbextensions-configurator
jupyter-contrib-nbextensions==0.7.0
    # via surf-malaga
jupyter-core==5.8.1
    # via ipykernel
    # via jupyter-client
    # via jupyter-console
    # via jupyter-contrib-core
    # via jupyter-contrib-nbextensions
    # via jupyter-nbextensions-configurator
    # via jupyter-server
    # via jupyterlab
    # via nbclient
    # via nbconvert
    # via nbformat
jupyter-events==0.12.0
    # via jupyter-server
jupyter-highlight-selected-word==0.2.0
    # via jupyter-contrib-nbextensions
jupyter-lsp==2.2.5
    # via jupyterlab
jupyter-nbextensions-configurator==0.6.4
    # via jupyter-contrib-nbextensions
jupyter-server==2.16.0
    # via jupyter-lsp
    # via jupyter-nbextensions-configurator
    # via jupyterlab
    # via jupyterlab-server
    # via notebook
    # via notebook-shim
jupyter-server-terminals==0.5.3
    # via jupyter-server
jupyterlab==4.4.4
    # via jupyter
    # via notebook
jupyterlab-pygments==0.3.0
    # via nbconvert
jupyterlab-server==2.27.3
    # via jupyterlab
    # via notebook
jupyterlab-widgets==3.0.15
    # via ipywidgets
kiwisolver==1.4.8
    # via matplotlib
lxml==6.0.0
    # via jupyter-contrib-nbextensions
    # via surf-malaga
markupsafe==3.0.2
    # via jinja2
    # via nbconvert
matplotlib==3.10.3
    # via seaborn
    # via surf-malaga
matplotlib-inline==0.1.7
    # via ipykernel
    # via ipython
mistune==3.1.3
    # via nbconvert
narwhals==1.46.0
    # via plotly
nbclient==0.10.2
    # via nbconvert
nbconvert==7.16.6
    # via jupyter
    # via jupyter-contrib-nbextensions
    # via jupyter-server
nbformat==5.10.4
    # via jupyter-server
    # via nbclient
    # via nbconvert
nest-asyncio==1.6.0
    # via ipykernel
notebook==7.4.4
    # via jupyter
    # via jupyter-contrib-core
    # via jupyter-contrib-nbextensions
    # via jupyter-nbextensions-configurator
notebook-shim==0.2.4
    # via jupyterlab
    # via notebook
numpy==2.3.1
    # via contourpy
    # via matplotlib
    # via pandas
    # via scikit-learn
    # via scipy
    # via seaborn
    # via surf-malaga
overrides==7.7.0
    # via jupyter-server
packaging==25.0
    # via ipykernel
    # via jupyter-events
    # via jupyter-server
    # via jupyterlab
    # via jupyterlab-server
    # via matplotlib
    # via nbconvert
    # via plotly
pandas==2.3.1
    # via seaborn
    # via surf-malaga
pandocfilters==1.5.1
    # via nbconvert
parso==0.8.4
    # via jedi
pdfminer-six==20250506
    # via pdfplumber
pdfplumber==0.11.7
    # via surf-malaga
pexpect==4.9.0
    # via ipython
pillow==11.3.0
    # via matplotlib
    # via pdfplumber
platformdirs==4.3.8
    # via jupyter-core
plotly==6.2.0
    # via surf-malaga
prometheus-client==0.22.1
    # via jupyter-server
prompt-toolkit==3.0.51
    # via ipython
    # via jupyter-console
psutil==7.0.0
    # via ipykernel
ptyprocess==0.7.0
    # via pexpect
    # via terminado
pure-eval==0.2.3
    # via stack-data
pyarrow==20.0.0
    # via surf-malaga
pycparser==2.22
    # via cffi
pygments==2.19.2
    # via ipython
    # via ipython-pygments-lexers
    # via jupyter-console
    # via nbconvert
pyparsing==3.2.3
    # via matplotlib
pypdfium2==4.30.1
    # via pdfplumber
python-dateutil==2.9.0.post0
    # via arrow
    # via jupyter-client
    # via matplotlib
    # via pandas
python-json-logger==3.3.0
    # via jupyter-events
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via jupyter-events
    # via jupyter-nbextensions-configurator
pyzmq==27.0.0
    # via ipykernel
    # via jupyter-client
    # via jupyter-console
    # via jupyter-server
referencing==0.36.2
    # via jsonschema
    # via jsonschema-specifications
    # via jupyter-events
requests==2.32.4
    # via jupyterlab-server
    # via surf-malaga
rfc3339-validator==0.1.4
    # via jsonschema
    # via jupyter-events
rfc3986-validator==0.1.1
    # via jsonschema
    # via jupyter-events
rpds-py==0.26.0
    # via jsonschema
    # via referencing
scikit-learn==1.7.0
    # via surf-malaga
scipy==1.16.0
    # via scikit-learn
seaborn==0.13.2
    # via surf-malaga
send2trash==1.8.3
    # via jupyter-server
setuptools==80.9.0
    # via jupyter-contrib-core
    # via jupyterlab
six==1.17.0
    # via python-dateutil
    # via rfc3339-validator
sniffio==1.3.1
    # via anyio
soupsieve==2.7
    # via beautifulsoup4
stack-data==0.6.3
    # via ipython
terminado==0.18.1
    # via jupyter-server
    # via jupyter-server-terminals
threadpoolctl==3.6.0
    # via scikit-learn
tinycss2==1.4.0
    # via bleach
tornado==6.5.1
    # via ipykernel
    # via jupyter-client
    # via jupyter-contrib-core
    # via jupyter-contrib-nbextensions
    # via jupyter-nbextensions-configurator
    # via jupyter-server
    # via jupyterlab
    # via notebook
    # via terminado
traitlets==5.14.3
    # via comm
    # via ipykernel
    # via ipython
    # via ipywidgets
    # via jupyter-client
    # via jupyter-console
    # via jupyter-contrib-core
    # via jupyter-contrib-nbextensions
    # via jupyter-core
    # via jupyter-events
    # via jupyter-nbextensions-configurator
    # via jupyter-server
    # via jupyterlab
    # via matplotlib-inline
    # via nbclient
    # via nbconvert
    # via nbformat
types-python-dateutil==2.9.0.20250708
    # via arrow
typing-extensions==4.14.1
    # via anyio
    # via beautifulsoup4
    # via referencing
tzdata==2025.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
urllib3==2.5.0
    # via requests
wcwidth==0.2.13
    # via prompt-toolkit
webcolors==24.11.1
    # via jsonschema
webencodings==0.5.1
    # via bleach
    # via tinycss2
websocket-client==1.8.0
    # via jupyter-server
widgetsnbextension==4.0.14
    # via ipywidgets
