#!/usr/bin/env python3
"""
Debug script to examine the meteo365.es page structure.
"""

import requests
from bs4 import BeautifulSoup
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
METEO365_URL = "https://meteo365.es/livecams/malaga.php"

def debug_page():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        response = session.get(METEO365_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print("=== PAGE TITLE ===")
        print(soup.title.get_text() if soup.title else "No title")
        
        print("\n=== LOOKING FOR WEATHER DATA ===")
        
        # Look for any text containing numbers and units
        all_text = soup.get_text()
        
        # Find patterns that look like measurements
        patterns = [
            r'(\d+(?:\.\d+)?)\s*(°[CF])',  # Temperature
            r'(\d+(?:\.\d+)?)\s*(km/h|mph|kts|m/s)',  # Wind speed
            r'(\d+(?:\.\d+)?)\s*%',  # Humidity
            r'(\d+(?:\.\d+)?)\s*(hPa|mbar)',  # Pressure
            r'(\d+(?:\.\d+)?)\s*m',  # Wave height
        ]
        
        print("Found measurement patterns:")
        for pattern in patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                print(f"Pattern {pattern}: {matches}")
        
        print("\n=== LOOKING FOR IMAGES ===")
        images = soup.find_all('img')
        print(f"Found {len(images)} images:")
        for i, img in enumerate(images[:10]):  # Show first 10
            src = img.get('src', '')
            alt = img.get('alt', '')
            print(f"  {i+1}. src='{src}' alt='{alt}'")
        
        print("\n=== LOOKING FOR IFRAMES ===")
        iframes = soup.find_all('iframe')
        print(f"Found {len(iframes)} iframes:")
        for i, iframe in enumerate(iframes):
            src = iframe.get('src', '')
            print(f"  {i+1}. src='{src}'")
        
        print("\n=== LOOKING FOR WEATHER STATION DATA ===")
        # Look for specific weather-related text
        weather_keywords = ['wind', 'temperature', 'humidity', 'pressure', 'weather', 'station']
        for keyword in weather_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            if elements:
                print(f"\nFound '{keyword}' in {len(elements)} places:")
                for elem in elements[:3]:  # Show first 3
                    parent = elem.parent
                    if parent:
                        context = parent.get_text().strip()[:100]
                        print(f"  Context: {context}")
        
        print("\n=== LOOKING FOR TABLES ===")
        tables = soup.find_all('table')
        print(f"Found {len(tables)} tables:")
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"  Table {i+1}: {len(rows)} rows")
            if rows:
                first_row = rows[0].get_text().strip()[:100]
                print(f"    First row: {first_row}")
        
        # Save the HTML for manual inspection
        with open('meteo365_debug.html', 'w', encoding='utf-8') as f:
            f.write(str(soup.prettify()))
        print(f"\nSaved HTML to meteo365_debug.html for manual inspection")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_page()
