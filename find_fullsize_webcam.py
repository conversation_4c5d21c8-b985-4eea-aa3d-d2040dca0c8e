#!/usr/bin/env python3
"""
Find the full-size webcam image for Malaga beach.
"""

import requests

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

def find_fullsize_webcam():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    # The thumbnail is at: thumbs/cams/malagaplaya.jpg
    # Let's try various patterns for the full-size image
    
    base_patterns = [
        "https://meteo365.es/livecams/cams/malagaplaya.jpg",
        "https://meteo365.es/livecams/images/malagaplaya.jpg", 
        "https://meteo365.es/livecams/malagaplaya.jpg",
        "https://meteo365.es/cams/malagaplaya.jpg",
        "https://cam.meteo365.es/malagaplaya.jpg",
        "https://cam.meteo365.es/malaga/malagaplaya.jpg",
        "https://cam.meteo365.es/malaga/current.jpg",
        "https://cam.meteo365.es/malaga/live.jpg",
        "https://cam.meteo365.es/malaga.jpg",
        "https://meteo365.es/livecams/full/malagaplaya.jpg",
        "https://meteo365.es/livecams/live/malagaplaya.jpg",
    ]
    
    # Also try different extensions and variations
    variations = [
        "malagaplaya.jpg",
        "malaga-playa.jpg", 
        "malaga_playa.jpg",
        "malaga.jpg",
        "playa.jpg",
        "beach.jpg",
        "current.jpg",
        "live.jpg",
        "cam.jpg",
        "webcam.jpg"
    ]
    
    base_urls = [
        "https://meteo365.es/livecams/cams/",
        "https://meteo365.es/livecams/images/",
        "https://meteo365.es/livecams/",
        "https://meteo365.es/cams/",
        "https://cam.meteo365.es/",
        "https://cam.meteo365.es/malaga/",
        "https://cam.meteo365.es/malagaplaya/",
    ]
    
    all_patterns = base_patterns.copy()
    
    # Generate combinations
    for base_url in base_urls:
        for variation in variations:
            all_patterns.append(base_url + variation)
    
    print(f"Testing {len(all_patterns)} patterns for full-size webcam image...")
    
    found_images = []
    
    for i, pattern in enumerate(all_patterns):
        try:
            print(f"\r{i+1}/{len(all_patterns)}: Testing {pattern}", end="", flush=True)
            response = session.get(pattern, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if content_type.startswith('image/'):
                    size = len(response.content)
                    print(f"\n✓ Found image: {pattern}")
                    print(f"  Content-Type: {content_type}")
                    print(f"  Size: {size} bytes")
                    
                    found_images.append({
                        'url': pattern,
                        'content_type': content_type,
                        'size': size,
                        'data': response.content
                    })
                    
                    # Save the image for inspection
                    filename = f"fullsize_{pattern.split('/')[-1]}"
                    with open(filename, 'wb') as f:
                        f.write(response.content)
                    print(f"  Saved to: {filename}")
                    
        except Exception as e:
            continue
    
    print(f"\n\nFound {len(found_images)} full-size images:")
    for img in found_images:
        print(f"  {img['url']} - {img['size']} bytes")
    
    # The largest image is likely the full-size webcam
    if found_images:
        largest = max(found_images, key=lambda x: x['size'])
        print(f"\nLargest image (likely full-size webcam): {largest['url']}")
        print(f"Size: {largest['size']} bytes")
        return largest['url']
    
    return None

if __name__ == "__main__":
    webcam_url = find_fullsize_webcam()
    if webcam_url:
        print(f"\nRecommended webcam URL: {webcam_url}")
    else:
        print("\nNo full-size webcam image found. Using thumbnail as fallback.")
        print("Fallback URL: https://meteo365.es/livecams/thumbs/cams/malagaplaya.jpg")
