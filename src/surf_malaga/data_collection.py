"""
Real-time data collection module for beach cam images, environmental data, and tide data.

This module provides functions to scrape data from:
- meteo365.es for beach cam images and environmental data
- tideking.com for tide data

The collected data is saved to organized directories with timestamps.
"""

import requests
import json
import re
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from bs4 import BeautifulSoup
import pandas as pd


# Realistic browser User-Agent to avoid being blocked
USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# URLs for data collection
METEO365_URL = "https://meteo365.es/livecams/malaga.php"
TIDEKING_URL = "https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta"


def get_session() -> requests.Session:
    """Create a requests session with realistic headers."""
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    return session


def collect_beach_cam_data() -> Dict[str, Any]:
    """
    Collect beach cam image from meteo365.es.

    Returns:
        Dict containing image URL, image data, and any metadata
        Note: image_data is returned separately to avoid JSON serialization issues
    """
    session = get_session()

    try:
        # Direct URL to the live webcam image (discovered through analysis)
        image_url = "https://meteo365.es/livecams/images/current.jpg"

        # Download the image
        img_response = session.get(image_url, timeout=30)
        img_response.raise_for_status()

        image_data = img_response.content
        image_size = len(image_data)

        # Verify it's actually an image
        content_type = img_response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            raise Exception(f"URL did not return an image: content-type={content_type}")

        # Return metadata without the binary data to avoid JSON serialization issues
        result = {
            'image_url': image_url,
            'image_size_bytes': image_size,
            'content_type': content_type,
            'collection_time': datetime.now().isoformat(),
            'source': 'meteo365.es',
            'camera_location': 'Malaga Beach La Malagueta'
        }

        # Store image data separately for saving to file
        result['_image_data'] = image_data

        return result

    except Exception as e:
        print(f"Error collecting beach cam data: {e}")
        return {
            'error': str(e),
            'collection_time': datetime.now().isoformat(),
            'source': 'meteo365.es'
        }


def collect_environmental_data() -> Dict[str, Any]:
    """
    Collect environmental data (wind, temperature, etc.) from meteo365.es.

    Returns:
        Dict containing environmental measurements
    """
    session = get_session()

    env_data = {
        'collection_time': datetime.now().isoformat(),
        'source': 'meteo365.es'
    }

    try:
        # First try the direct weather data URL
        weather_data_url = "https://cam.meteo365.es/station/malaga.txt"

        response = session.get(weather_data_url, timeout=30)

        if response.status_code == 200 and not response.text.strip().startswith('<!doctype'):
            # Parse the weather data text file
            lines = response.text.strip().split('\n')
            env_data['raw_data_url'] = weather_data_url

            if len(lines) >= 9:
                try:
                    # Parse the data based on the JavaScript parsing logic seen in the HTML
                    # Line 0: temperature (format: "temp:XX.X")
                    temp_line = lines[0].split(':')
                    if len(temp_line) > 1:
                        temp_str = temp_line[1].strip().replace(',', '.')
                        if temp_str != "60.0":  # 60.0 seems to be a "no data" value
                            env_data['temperature_c'] = float(temp_str)

                    # Line 2: wind speed (format: "wind XX.X")
                    wind_line = lines[2].split(' ')
                    if len(wind_line) > 1:
                        env_data['wind_speed_kmh'] = float(wind_line[1])

                    # Line 3: wind gusts (format: "gusts XX.X")
                    gusts_line = lines[3].split(' ')
                    if len(gusts_line) > 1:
                        env_data['wind_gusts_kmh'] = float(gusts_line[1])

                    # Line 4: humidity (format: "humidity:XX%")
                    humidity_line = lines[4].split(':')
                    if len(humidity_line) > 1:
                        humidity_str = humidity_line[1].replace('%', '').strip()
                        env_data['humidity_percent'] = float(humidity_str)

                    # Line 5: rain (format: "rain XX.X")
                    rain_line = lines[5].split(' ')
                    if len(rain_line) > 3:
                        env_data['rain_mm'] = float(rain_line[3])

                    # Line 6: pressure (format: "pressure XX.X")
                    pressure_line = lines[6].split(' ')
                    if len(pressure_line) > 2:
                        env_data['pressure_hpa'] = float(pressure_line[2])

                    # Line 8: wind direction (format: "wind_dir XXX")
                    wind_dir_line = lines[8].split(' ')
                    if len(wind_dir_line) > 2:
                        env_data['wind_direction_deg'] = float(wind_dir_line[2])

                    # Line 9: timestamp
                    if len(lines) > 9:
                        timestamp_str = lines[9].replace('-', '/').replace('T', ' ').replace('Z', ' ').strip()
                        env_data['station_timestamp'] = timestamp_str

                    return env_data

                except (ValueError, IndexError) as e:
                    env_data['parsing_error'] = f"Error parsing weather data: {e}"
                    env_data['raw_lines'] = lines
            else:
                env_data['error'] = f"Insufficient data lines: got {len(lines)}, expected at least 9"
                env_data['raw_lines'] = lines
        else:
            # Fallback: scrape the main page for any available weather info
            env_data['fallback_method'] = 'main_page_scraping'
            env_data['direct_url_status'] = response.status_code

            main_response = session.get(METEO365_URL, timeout=30)
            main_response.raise_for_status()

            soup = BeautifulSoup(main_response.content, 'html.parser')

            # Look for any weather-related text patterns in the page
            page_text = soup.get_text()

            # Try to find temperature patterns
            temp_matches = re.findall(r'(\d+(?:\.\d+)?)\s*°([CF])', page_text)
            if temp_matches:
                env_data['temperature_candidates'] = temp_matches

            # Try to find wind speed patterns
            wind_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(km/h|mph|kts|m/s)', page_text)
            if wind_matches:
                env_data['wind_speed_candidates'] = wind_matches

            # Try to find humidity patterns
            humidity_matches = re.findall(r'(\d+(?:\.\d+)?)\s*%', page_text)
            if humidity_matches:
                env_data['humidity_candidates'] = humidity_matches

            # Try to find pressure patterns
            pressure_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(hPa|mbar)', page_text)
            if pressure_matches:
                env_data['pressure_candidates'] = pressure_matches

        return env_data

    except Exception as e:
        print(f"Error collecting environmental data: {e}")
        env_data['error'] = str(e)
        return env_data


def collect_tide_data() -> Dict[str, Any]:
    """
    Collect current tide data from tideking.com and interpolate current height.
    
    Returns:
        Dict containing tide information and interpolated current height
    """
    session = get_session()
    
    try:
        response = session.get(TIDEKING_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        tide_data = {
            'collection_time': datetime.now().isoformat(),
            'source': 'tideking.com'
        }
        
        # Look for tide table data
        # The page has a table with tide times and heights
        
        # Find tide times and heights for today
        tide_times = []
        
        # Look for tide table rows
        for row in soup.find_all('tr'):
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 4:
                for i, cell in enumerate(cells):
                    text = cell.get_text().strip()
                    # Look for time patterns like "7:50 am" or "8:22 pm"
                    time_match = re.search(r'(\d{1,2}):(\d{2})\s*(am|pm)', text)
                    if time_match:
                        hour = int(time_match.group(1))
                        minute = int(time_match.group(2))
                        period = time_match.group(3)
                        
                        # Convert to 24-hour format
                        if period == 'pm' and hour != 12:
                            hour += 12
                        elif period == 'am' and hour == 12:
                            hour = 0
                        
                        # Look for height in the same row
                        height = None
                        for next_cell in cells[i+1:]:
                            height_text = next_cell.get_text().strip()
                            height_match = re.search(r'([+-]?\d+(?:\.\d+)?)\s*m', height_text)
                            if height_match:
                                height = float(height_match.group(1))
                                break
                        
                        if height is not None:
                            tide_times.append({
                                'time': f"{hour:02d}:{minute:02d}",
                                'height_m': height,
                                'type': 'high' if '▲' in text else 'low' if '▼' in text else 'unknown'
                            })
        
        tide_data['tide_times'] = tide_times
        
        # Interpolate current tide height
        if len(tide_times) >= 2:
            current_height = interpolate_tide_height(tide_times)
            tide_data['current_height_m'] = current_height
        
        return tide_data
        
    except Exception as e:
        print(f"Error collecting tide data: {e}")
        return {
            'error': str(e),
            'collection_time': datetime.now().isoformat(),
            'source': 'tideking.com'
        }


def interpolate_tide_height(tide_times: list) -> Optional[float]:
    """
    Interpolate current tide height based on tide times and heights.
    
    Args:
        tide_times: List of dicts with 'time' and 'height_m' keys
        
    Returns:
        Interpolated tide height in meters, or None if interpolation fails
    """
    if len(tide_times) < 2:
        return None
    
    try:
        current_time = datetime.now()
        current_minutes = current_time.hour * 60 + current_time.minute
        
        # Convert tide times to minutes since midnight
        tide_points = []
        for tide in tide_times:
            time_parts = tide['time'].split(':')
            tide_minutes = int(time_parts[0]) * 60 + int(time_parts[1])
            tide_points.append((tide_minutes, tide['height_m']))
        
        # Sort by time
        tide_points.sort()
        
        # Find the two tide points to interpolate between
        before_point = None
        after_point = None
        
        for i, (time_min, height) in enumerate(tide_points):
            if time_min <= current_minutes:
                before_point = (time_min, height)
            else:
                after_point = (time_min, height)
                break
        
        # If we're after all tide times, use the last two points
        if after_point is None and len(tide_points) >= 2:
            before_point = tide_points[-2]
            after_point = tide_points[-1]
            # Extrapolate forward
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_since_last = current_minutes - after_point[0]
            if time_since_last > 0:
                slope = height_diff / time_diff if time_diff != 0 else 0
                return after_point[1] + slope * time_since_last
        
        # If we're before all tide times, use the first two points
        if before_point is None and len(tide_points) >= 2:
            before_point = tide_points[0]
            after_point = tide_points[1]
            # Extrapolate backward
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_before_first = before_point[0] - current_minutes
            if time_before_first > 0:
                slope = height_diff / time_diff if time_diff != 0 else 0
                return before_point[1] - slope * time_before_first
        
        # Normal interpolation between two points
        if before_point and after_point:
            time_diff = after_point[0] - before_point[0]
            height_diff = after_point[1] - before_point[1]
            time_progress = (current_minutes - before_point[0]) / time_diff if time_diff != 0 else 0
            return before_point[1] + height_diff * time_progress
        
        return None
        
    except Exception as e:
        print(f"Error interpolating tide height: {e}")
        return None


def save_collection_data(data: Dict[str, Any], image_data: Optional[bytes], 
                        timestamp: datetime) -> Dict[str, str]:
    """
    Save collected data to organized directory structure.
    
    Args:
        data: Combined data dictionary to save as JSON
        image_data: Beach cam image data (bytes)
        timestamp: Collection timestamp
        
    Returns:
        Dict with paths where data was saved
    """
    # Create directory structure: data/external/year/month/day
    base_dir = Path("data/external")
    date_dir = base_dir / str(timestamp.year) / f"{timestamp.month:02d}" / f"{timestamp.day:02d}"
    date_dir.mkdir(parents=True, exist_ok=True)
    
    # Create filename with timestamp
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
    
    saved_paths = {}
    
    # Save JSON data
    json_path = date_dir / f"{timestamp_str}.json"
    with open(json_path, 'w') as f:
        json.dump(data, f, indent=2)
    saved_paths['json_path'] = str(json_path)
    
    # Save image if available
    if image_data:
        image_path = date_dir / f"{timestamp_str}.jpg"
        with open(image_path, 'wb') as f:
            f.write(image_data)
        saved_paths['image_path'] = str(image_path)
    
    return saved_paths
