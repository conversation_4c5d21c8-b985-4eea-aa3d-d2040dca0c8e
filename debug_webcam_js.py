#!/usr/bin/env python3
"""
Debug script to find the actual live webcam feed by examining JavaScript.
"""

import requests
from bs4 import BeautifulSoup
import re

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
METEO365_URL = "https://meteo365.es/livecams/malaga.php"

def find_webcam_js():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    try:
        response = session.get(METEO365_URL, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print("=== LOOKING FOR WEBCAM JAVASCRIPT ===")
        
        # Look for all script tags
        scripts = soup.find_all('script')
        
        for i, script in enumerate(scripts):
            if script.string:
                text = script.string
                
                # Look for blob URLs or webcam-related patterns
                blob_urls = re.findall(r'blob:[^"\'\s]+', text)
                if blob_urls:
                    print(f"Script {i}: Found blob URLs: {blob_urls}")
                
                # Look for UUID patterns
                uuid_patterns = re.findall(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', text)
                if uuid_patterns:
                    print(f"Script {i}: Found UUIDs: {uuid_patterns}")
                
                # Look for image loading or webcam-related functions
                if any(keyword in text.lower() for keyword in ['webcam', 'camera', 'stream', 'live', 'image', 'src']):
                    lines = text.split('\n')
                    for line_num, line in enumerate(lines):
                        if any(keyword in line.lower() for keyword in ['webcam', 'camera', 'stream', 'live']):
                            print(f"Script {i}, Line {line_num}: {line.strip()}")
                
                # Look for dynamic image loading
                img_src_patterns = re.findall(r'\.src\s*=\s*["\']([^"\']+)["\']', text)
                if img_src_patterns:
                    print(f"Script {i}: Dynamic image sources: {img_src_patterns}")
                
                # Look for fetch or XMLHttpRequest calls
                fetch_patterns = re.findall(r'fetch\s*\(\s*["\']([^"\']+)["\']', text)
                if fetch_patterns:
                    print(f"Script {i}: Fetch URLs: {fetch_patterns}")
                
                xhr_patterns = re.findall(r'\.open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']', text)
                if xhr_patterns:
                    print(f"Script {i}: XHR URLs: {xhr_patterns}")
        
        # Look for any external script sources that might load the webcam
        print("\n=== EXTERNAL SCRIPTS ===")
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            print(f"External script: {src}")
            
            # Try to fetch and analyze external scripts
            if src.startswith('http') or src.startswith('//'):
                try:
                    if src.startswith('//'):
                        src = 'https:' + src
                    
                    ext_response = session.get(src, timeout=10)
                    if ext_response.status_code == 200:
                        ext_text = ext_response.text
                        
                        # Look for webcam-related content in external scripts
                        if any(keyword in ext_text.lower() for keyword in ['webcam', 'camera', 'stream', 'live']):
                            print(f"  External script {src} contains webcam-related code")
                            
                            # Look for image URLs
                            img_urls = re.findall(r'["\']([^"\']*\.(?:jpg|jpeg|png|gif|webp))["\']', ext_text)
                            if img_urls:
                                print(f"    Image URLs: {img_urls}")
                
                except Exception as e:
                    print(f"  Error fetching {src}: {e}")
        
        # Look for any data attributes or hidden elements that might contain webcam info
        print("\n=== DATA ATTRIBUTES AND HIDDEN ELEMENTS ===")
        for element in soup.find_all(attrs={'data-src': True}):
            print(f"Element with data-src: {element.get('data-src')}")
        
        for element in soup.find_all(attrs={'data-url': True}):
            print(f"Element with data-url: {element.get('data-url')}")
        
        for element in soup.find_all(style=re.compile(r'display:\s*none', re.I)):
            if element.get_text().strip():
                print(f"Hidden element text: {element.get_text().strip()[:100]}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    find_webcam_js()
