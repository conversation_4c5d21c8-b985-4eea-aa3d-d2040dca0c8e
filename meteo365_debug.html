<!DOCTYPE html>
<html lang="en-EN">
 <head>
  <meta charset="utf-8"/>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
   <meta content="english" name="language"/>
   <title>
    meteo365.es - Webcam Málaga - Gran Hotel Miramar
   </title>
   <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
    <meta content="meteo365.es | Webcam en Malaga - Gran Hotel Miramar - Playa La Malagueta" name="title"/>
    <meta content="Webcams, live, Andalusia, Spain, Malaga, Valencia, Marbella, Torremolinos, Benalmadena, Weather, Sun, Costa del Sol, Costa Blanca, News, Beach" name="keywords"/>
    <meta content="Live Webcam in Málaga capital, Playa La Malagueta. Weather at the Costa del Sol in Andalusia, one of the most famous vacation destinations in Spain" name="description"/>
    <meta content="600" http-equiv="refresh"/>
    <meta content="summary_large_image" property="twitter:card"/>
    <meta content="https://meteo365.es/livecams/malaga-live.php" property="twitter:url"/>
    <meta content="meteo365.es | Webcam in Malaga - Gran Hotel Miramar - Playa La Malagueta" property="twitter:title"/>
    <meta content="Live Webcam in Málaga capital, Playa La Malagueta. Weather at the Costa del Sol in Andalusia, one of the most famous vacation destinations in Spain" property="twitter:description"/>
    <meta content="https://meteo365.es/livecams/cams/overlay/cammiramar.png" property="twitter:image"/>
    <meta content="@meteo365_es" name="twitter:site">
     <meta content="@meteo365_es" name="twitter:creator">
      <meta content="meteo365.es | Webcam in Malaga - Gran Hotel Miramar - Playa La Malagueta" property="og:title">
       <meta content="Live Webcam in Málaga capital, Playa La Malagueta. Weather at the Costa del Sol in Andalusia, one of the most famous vacation destinations in Spain" property="og:description"/>
       <meta content="article" property="og:type"/>
       <meta content="https://meteo365.es/livecams/malaga-live.php" property="og:url">
        <meta content="meteo365.es" property="og:site_name"/>
        <meta content="https://meteo365.es/livecams/cams/overlay/cammiramar.png" property="og:image">
         <meta content="https://meteo365.es/livecams/cams/overlay/cammiramar.png" property="og:image:secure_url">
          <meta content="image/png" property="og:image:type">
           <meta content="1024" property="og:image:width">
            <meta content="768" property="og:image:height">
             <meta content="all" name="robots">
              <meta content="1 days" name="revisit-after">
               <meta content="all" name="googlebot">
                <meta content="2025, mawe mediaonline SLU, all rights reserved." name="copyright"/>
                <meta content="max-age=0" http-equiv="cache-control">
                 <meta content="no-cache" http-equiv="cache-control"/>
                 <meta content="0" http-equiv="expires"/>
                 <meta content="no-cache" http-equiv="pragma"/>
                 <link href="https://meteo365.es/livecams/cams/overlay/cammiramar.png" rel="image_src"/>
                 <?php// include('../icons.php'); ?>
                 <link href="https://meteo365.es/img/icons/apple-icon-57x57.png" rel="apple-touch-icon" sizes="57x57"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-60x60.png" rel="apple-touch-icon" sizes="60x60"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-72x72.png" rel="apple-touch-icon" sizes="72x72"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-76x76.png" rel="apple-touch-icon" sizes="76x76"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-114x114.png" rel="apple-touch-icon" sizes="114x114"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-120x120.png" rel="apple-touch-icon" sizes="120x120"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-144x144.png" rel="apple-touch-icon" sizes="144x144"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-152x152.png" rel="apple-touch-icon" sizes="152x152"/>
                 <link href="https://meteo365.es/img/icons/apple-icon-180x180.png" rel="apple-touch-icon" sizes="180x180"/>
                 <link href="https://meteo365.es/img/icons/android-icon-192x192.png" rel="icon" sizes="192x192" type="image/png"/>
                 <link href="https://meteo365.es/img/icons/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png"/>
                 <link href="https://meteo365.es/img/icons/favicon-96x96.png" rel="icon" sizes="96x96" type="image/png"/>
                 <link href="https://meteo365.es/img/icons/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png"/>
                 <link href="https://meteo365.es/img/icons/manifest.json" rel="manifest"/>
                 <meta content="#ffffff" name="msapplication-TileColor"/>
                 <meta content="https://meteo365.es/img/cons/ms-icon-144x144.png" name="msapplication-TileImage"/>
                 <meta content="#ffffff" name="theme-color"/>
                 <link href="https://fonts.googleapis.com/css?family=Quicksand" rel="stylesheet"/>
                 <script src="https://polyfill.io/v3/polyfill.min.js?features=ResizeObserver" type="application/javascript">
                 </script>
                 <script language="JavaScript">
                  var numBAH = Math.floor(Math.random()*10000);document.write('<LI' + 'NK HREF="cssneu/livecams.css?cacheBusting='+numBAH+'" rel="stylesheet">'); document.write('<LI' + 'NK HREF="cssneu/elite.css?cacheBusting='+numBAH+'" rel="stylesheet">');
                 </script>
                 <link href="cssneu/elite-font-awesome.css" rel="stylesheet" type="text/css"/>
                 <link href="cssneu/jquery.mCustomScrollbar.css" rel="stylesheet" type="text/css"/>
                 <script src="jsneu/chart.js">
                 </script>
                 <script src="jsneu/chartjs-datalabels">
                 </script>
                 <script src="jsneu/jquery-3.2.1.min.js">
                 </script>
                 <script src="jsneu/hls.js">
                 </script>
                 <script src="jsneu/froogaloop.js" type="text/javascript">
                 </script>
                 <script src="jsneu/jquery.mCustomScrollbar.js" type="text/javascript">
                 </script>
                 <script src="jsneu/THREEx.FullScreen.js">
                 </script>
                 <script src="jsneu/videoPlayer.js" type="text/javascript">
                 </script>
                 <script src="jsneu/Playlist.js" type="text/javascript">
                 </script>
                 <script src="jsneu/vast.js" type="text/javascript">
                 </script>
                 <script src="jsneu/gauge.js" type="text/javascript">
                 </script>
                 <script src="jsneu/sun-card.js">
                 </script>
                 <script charset="utf-8" src="jscams/cam-playa.js?t=123" type="text/javascript">
                 </script>
                </meta>
               </meta>
              </meta>
             </meta>
            </meta>
           </meta>
          </meta>
         </meta>
        </meta>
       </meta>
      </meta>
     </meta>
    </meta>
   </meta>
  </meta>
 </head>
 <body>
  <header>
   <div id="headerbereich">
    <nav id="headercontent">
     <ul id="nav">
      <li>
       <a href="https://meteo365.es/" target="_self">
        <div id="navcams">
         <svg class="bi bi-brightness-high" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z">
          </path>
         </svg>
         WeatherMap
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/weather/observation/" target="_self">
        <div id="navcams">
         <svg class="bi bi-globe-americas" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0ZM2.04 4.326c.325 1.329 2.532 2.54 3.717 3.19.48.263.793.434.743.484-.08.08-.162.158-.242.234-.416.396-.787.749-.758 1.266.035.634.618.824 1.214 1.017.577.188 1.168.38 1.286.983.082.417-.075.988-.22 1.52-.215.782-.406 1.48.22 1.48 1.5-.5 3.798-3.186 4-5 .138-1.243-2-2-3.5-2.5-.478-.16-.755.081-.99.284-.172.15-.322.279-.51.216-.445-.148-2.5-2-1.5-2.5.78-.39.952-.171 1.227.182.078.099.163.208.273.318.609.304.662-.132.723-.633.039-.322.081-.671.277-.867.434-.434 1.265-.791 2.028-1.12.712-.306 1.365-.587 1.579-.88A7 7 0 1 1 2.04 4.327Z">
          </path>
         </svg>
         Observation
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/livecams/" target="_self">
        <div id="navcams">
         <svg class="bi bi-camera" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h1.172a3 3 0 0 0 2.12-.879l.83-.828A1 1 0 0 1 6.827 3h2.344a1 1 0 0 1 .707.293l.828.828A3 3 0 0 0 12.828 5H14a1 1 0 0 1 1 1v6zM2 4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-1.172a2 2 0 0 1-1.414-.586l-.828-.828A2 2 0 0 0 9.172 2H6.828a2 2 0 0 0-1.414.586l-.828.828A2 2 0 0 1 3.172 4H2z">
          </path>
          <path d="M8 11a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm0 1a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zM3 6.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0z">
          </path>
         </svg>
         LiveCams
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/airport/" target="_self">
        <div id="navcams">
         <svg class="bi bi-airplane" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M6.428 1.151C6.708.591 7.213 0 8 0s1.292.592 1.572 1.151C9.861 1.73 10 2.431 10 3v3.691l5.17 2.585a1.5 1.5 0 0 1 .83 1.342V12a.5.5 0 0 1-.582.493l-5.507-.918-.375 2.253 1.318 1.318A.5.5 0 0 1 10.5 16h-5a.5.5 0 0 1-.354-.854l1.319-1.318-.376-2.253-5.507.918A.5.5 0 0 1 0 12v-1.382a1.5 1.5 0 0 1 .83-1.342L6 6.691V3c0-.568.14-1.271.428-1.849Zm.894.448C7.111 2.02 7 2.569 7 3v4a.5.5 0 0 1-.276.447l-5.448 2.724a.5.5 0 0 0-.276.447v.792l5.418-.903a.5.5 0 0 1 .575.41l.5 3a.5.5 0 0 1-.14.437L6.708 15h2.586l-.647-.646a.5.5 0 0 1-.14-.436l.5-3a.5.5 0 0 1 .576-.411L15 11.41v-.792a.5.5 0 0 0-.276-.447L9.276 7.447A.5.5 0 0 1 9 7V3c0-.432-.11-.979-.322-1.401C8.458 1.159 8.213 1 8 1c-.213 0-.458.158-.678.599Z">
          </path>
         </svg>
         AviationWeather
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/news/" target="_self">
        <div id="navcams">
         <svg class="bi bi-newspaper" fill="currentColor" height="14" viewbox="0 0 16 16" width="14" xmlns="http://www.w3.org/2000/svg">
          <path d="M0 2.5A1.5 1.5 0 0 1 1.5 1h11A1.5 1.5 0 0 1 14 2.5v10.528c0 .3-.05.654-.238.972h.738a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 1 1 0v9a1.5 1.5 0 0 1-1.5 1.5H1.497A1.497 1.497 0 0 1 0 13.5v-11zM12 14c.37 0 .654-.211.853-.441.092-.106.147-.279.147-.531V2.5a.5.5 0 0 0-.5-.5h-11a.5.5 0 0 0-.5.5v11c0 .278.223.5.497.5H12z">
          </path>
          <path d="M2 3h10v2H2V3zm0 3h4v3H2V6zm0 4h4v1H2v-1zm0 2h4v1H2v-1zm5-6h2v1H7V6zm3 0h2v1h-2V6zM7 8h2v1H7V8zm3 0h2v1h-2V8zm-3 2h2v1H7v-1zm3 0h2v1h-2v-1zm-3 2h2v1H7v-1zm3 0h2v1h-2v-1z">
          </path>
         </svg>
         News
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/earthquakes/" target="_self">
        <div id="navcams">
         <svg class="bi bi-activity" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2Z" fill-rule="evenodd">
          </path>
         </svg>
         Earthquakes
        </div>
       </a>
      </li>
      <li>
       <a href="https://meteo365.es/timezones/" target="_self">
        <svg class="bi bi-clock" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
         <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z">
         </path>
         <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z">
         </path>
        </svg>
        TimeZones
       </a>
      </li>
      <li class="more">
       <span>
        <svg class="bi bi-three-dots-vertical" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
         <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z">
         </path>
        </svg>
       </span>
       <ul id="overflow">
       </ul>
      </li>
     </ul>
     <div id="dropdown">
      <button class="dropbtn">
       <img src="../../airport/images/en.png"/>
       <svg class="bi bi-caret-down-fill" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
        <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z">
        </path>
       </svg>
      </button>
      <div class="dropdown-content">
       <!-- <center><a href=""><span onClick="sprache('de');"><img src="../../airport/images/de.png" /></span></a></center>
    <center><a href=""><span onClick="sprache('es');"><img src="../../airport/images/es.png" /></span></a></center>-->
       <form action="" id="Test" method="post">
        <center>
         <button class="border" name="deutsch" type="submit">
          <img src="img/de.png"/>
         </button>
        </center>
        <center>
         <button class="border" name="espanol" type="submit">
          <img src="img/es.png"/>
         </button>
        </center>
       </form>
      </div>
     </div>
    </nav>
    <a href="https://meteo365.es/">
     <div id="logo">
      meteo365.es
     </div>
    </a>
    <!--<div id="logo" onclick="location.href='https://meteo365.es/';"></div>-->
   </div>
   <script>
    window.onresize = navigationResize;
navigationResize();

function navigationResize() {  
  $('#nav li.more').before($('#overflow > li'));
  
  var $navItemMore = $('#nav > li.more'),
  		$navItems = $('#nav > li:not(.more)'),
      navItemMoreWidth = navItemWidth = $navItemMore.width(),
      windowWidth = document.getElementById('headercontent').clientWidth,
      //windowWidth = $(window).width(),
      navItemMoreLeft, offset, navOverflowWidth;
    
   // if($(window).width()>=600) { windowWidth = document.getElementById('headercontent').clientWidth+100;} else { windowWidth = document.getElementById('headercontent').clientWidth+50;}
  
  $navItems.each(function() {
    navItemWidth += $(this).width(); 
  });
  
    if($(window).width()>=600) { navItemWidth += parseFloat(100);} else { navItemWidth += parseFloat(40);}
  navItemWidth > windowWidth ? $navItemMore.show() : $navItemMore.hide();
     console.log("navItemWidth: "+navItemWidth+" windowWidth: "+windowWidth);
    
  while (navItemWidth > windowWidth) {
    navItemWidth -= $navItems.last().width();
    $navItems.last().prependTo('#overflow');
    $navItems.splice(-1,1);
  }
  
  navItemMoreLeft = $('#nav .more').offset().left;
  navOverflowWidth = $('#overflow').width();  
  offset = navItemMoreLeft + navItemMoreWidth - navOverflowWidth;
    
  $('#overflow').css({
    'left': offset
  });
}
    
$(window).resize(function() {
  var width = $(window).width();
  var height = $(window).height();
    console.log(width);
})    
  
$(function() {
    var width = $(window).width();
    if(width<=600) { 
  $('#nav .more').hover(function() {
    $('#logo').css('display', 'none');
  }, function() {
    // on mouseout, reset the background colour
    $('#logo').css('display', 'block');
  });
        }
});
   </script>
  </header>
  <div id="soistes">
  </div>
  <div class="ads showads">
   <!--      <a href="https://meteo365.es/livecams/timelapse.php"> <div id="" style=" border-radius: 30px;  padding: 10px ; color: #fff; text-shadow: 2px 2px 4px #03111e; letter-spacing: 1px; font-weight: 100; display: table; box-shadow: 1px 1px 4px #000; width: auto; margin: 15px auto 15px; background-color: #f29400;"> <span style="color: darkblue;">N E W:</span> Beautiful time-lapse videos of our webcams</div>  </a>

  <div class="wetterspanien"><big>Weather in Spain, Andalusia & Costa del Sol</big> - Our weather for Fuengirola: If rain, wind, storm, waves, snow or sun, our ongoing hourly weather forecast for the next 10 days for Fuengirola on <a href="https://meteo365.es/?36.73,-4.40,9&temp" target="_blank">meteo365.es</a> </div>  
-->
   <div class="content1">
    <div class="contentinhalt">
     <h1>
      Live Webcam in Málaga, Playa La Malagueta
     </h1>
    </div>
   </div>
   <div class="content2">
    <div class="contentinhalt">
     <div id="werbungum" style="margin-bottom: 15px;">
      <center>
       <span id="werbung3">
       </span>
      </center>
     </div>
     <center>
      <div id="livecam_videok">
       <div id="livecam_video">
       </div>
      </div>
     </center>
     <div id="camlugar">
      <a href="https://www.granhotelmiramarmalaga.com/en/deals/" target="_blank">
       <div class="ort">
        <svg class="bi bi-geo-fill" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
         <path d="M4 4a4 4 0 1 1 4.5 3.969V13.5a.5.5 0 0 1-1 0V7.97A4 4 0 0 1 4 3.999zm2.493 8.574a.5.5 0 0 1-.411.575c-.712.118-1.28.295-1.655.493a1.319 1.319 0 0 0-.37.265.301.301 0 0 0-.057.09V14l.002.008a.147.147 0 0 0 .016.033.617.617 0 0 0 .145.15c.165.13.435.27.813.395.751.25 1.82.414 3.024.414s2.273-.163 3.024-.414c.378-.126.648-.265.813-.395a.619.619 0 0 0 .146-.15.148.148 0 0 0 .015-.033L12 14v-.004a.301.301 0 0 0-.057-.09 1.318 1.318 0 0 0-.37-.264c-.376-.198-.943-.375-1.655-.493a.5.5 0 1 1 .164-.986c.77.127 1.452.328 1.957.594C12.5 13 13 13.4 13 14c0 .426-.26.752-.544.977-.29.228-.68.413-1.116.558-.878.293-2.059.465-3.34.465-1.281 0-2.462-.172-3.34-.465-.436-.145-.826-.33-1.116-.558C3.26 14.752 3 14.426 3 14c0-.599.5-1 .961-1.243.505-.266 1.187-.467 1.957-.594a.5.5 0 0 1 .575.411z" fill-rule="evenodd">
         </path>
        </svg>
        Gran Hotel Miramar
       </div>
      </a>
     </div>
     <h2 id="airport">
      La Malagueta beach, between the Port of Málaga and La Caleta beach, is one of the most famous beaches for both tourists and locals
     </h2>
     <button id="buttonwebcams" onclick="showcams();">
      All webcams
     </button>
     <div style="clear: both;">
     </div>
    </div>
    <div style="clear: both;">
    </div>
   </div>
   <div id="werbungum">
    <br/>
    <center>
     <span id="werbung_phone">
     </span>
    </center>
   </div>
   <div class="content2">
    <div class="contentinhalt">
     <script>
      var horoai=2;
     </script>
     <br/>
     <div style="clear: both;">
     </div>
     <div id="contentcams">
      <div id="wetterstationbox2">
       <h4>
        meteo365.es weather station
        <strong>
         Málaga (Beach)
        </strong>
        <div class="wetterstationgemessen">
         Measured:
         <span id="emzeit">
         </span>
        </div>
       </h4>
       <br/>
       <div class="cardwind">
        <div class="card">
         <div class="compass">
          <svg viewbox="0 0 331 331" xmlns="http://www.w3.org/2000/svg">
           <g fill="none" fill-rule="evenodd" opacity=".401">
            <path d="M11.5 0 23 23H0z" fill="#000" transform="translate(154 31)">
            </path>
            <path d="m11.421 250 .158 17.999" stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2" transform="translate(154 31)">
            </path>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(6 -241.38 1723.694)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(12 -78.745 934.435)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(18 -24.333 670.385)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(24 3.021 537.632)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(30 19.555 457.394)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(36 30.679 403.409)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(42 38.713 364.42)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(48 44.816 334.798)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(54 49.636 311.415)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(60 53.555 292.394)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(66 56.822 276.539)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(72 59.601 263.051)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(78 62.007 251.379)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(84 64.12 241.125)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(90 66 232)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(96 67.694 223.784)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(102 69.233 216.307)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(108 70.649 209.44)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(114 71.96 203.075)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(120 73.185 197.131)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(126 74.338 191.536)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(132 75.431 186.231)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(138 76.474 181.169)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(144 77.477 176.306)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel" stroke-width="2">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(150 78.445 171.606)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(156 79.386 167.036)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(162 80.307 162.566)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(168 81.213 158.171)">
             </path>
            </g>
            <g stroke="#000" stroke-linecap="square" stroke-linejoin="bevel">
             <path d="m.421 0 .158 17.999M.421 247l.158 17.999" transform="rotate(174 82.109 153.824)">
             </path>
            </g>
            <g fill="#000" font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold">
             <text transform="translate(60 60)">
              <tspan x="92.501" y="33">
               N
              </tspan>
             </text>
             <text transform="translate(60 60)">
              <tspan x="178.494" y="118">
               E
              </tspan>
             </text>
             <text transform="translate(60 60)">
              <tspan x="93.494" y="203">
               S
              </tspan>
             </text>
             <text transform="translate(60 60)">
              <tspan x="3.511" y="118">
               W
              </tspan>
             </text>
            </g>
           </g>
          </svg>
          <div class="compass-content">
           <img id="winddirection" src="img/N.png"/>
          </div>
         </div>
        </div>
       </div>
       <div id="box1">
        <div class="wettercamstemp">
         <span id="emtemp">
         </span>
         <small>
          <span class="einheittemp">
          </span>
         </small>
        </div>
        <div id="winddirection2">
        </div>
        <div id="bfscala">
        </div>
       </div>
       <div id="box2a">
        <table id="wetterstation3">
         <tr>
          <td>
           Wind
          </td>
          <td>
           Gusts
          </td>
          <td>
           Beaufort
          </td>
         </tr>
         <tr>
          <td>
           <big>
            <span id="emwind">
            </span>
           </big>
          </td>
          <td>
           <big>
            <span id="emgusts">
            </span>
           </big>
          </td>
          <td rowspan="2">
           <span id="emwindbild">
           </span>
          </td>
         </tr>
         <tr>
          <td>
           <small>
            <span class="einheitboe">
            </span>
           </small>
          </td>
          <td>
           <small>
            <span class="einheitboe">
            </span>
           </small>
          </td>
         </tr>
        </table>
        <table id="wetterstation3">
         <tr>
          <td>
           Rain
          </td>
          <td>
           Humidity
          </td>
          <td>
           Pression
          </td>
         </tr>
         <tr>
          <td>
           <big>
            <span id="emrain">
            </span>
           </big>
          </td>
          <td>
           <big>
            <span id="emhum">
            </span>
           </big>
          </td>
          <td>
           <big>
            <span id="empres">
            </span>
           </big>
          </td>
         </tr>
         <tr>
          <td>
           <small>
            <span class="einheitrain">
            </span>
           </small>
          </td>
          <td>
           <small>
            %
           </small>
          </td>
          <td>
           <small>
            hPa
           </small>
          </td>
         </tr>
        </table>
       </div>
       <div style="clear: both;">
       </div>
       <div id="wetterstationbox2">
        <div class="chart-container2">
         <h4>
          Weather data the last 6 hours: Temperatures (in
          <span class="einheittemp">
          </span>
          ), gusts &amp; wind (in
          <span class="einheitboe">
          </span>
          ) &amp; rain (in
          <span class="einheitrain">
          </span>
          )
         </h4>
         <div class="chart-container">
          <div class="legend">
           <br/>
           <span id="lasttemp">
            T
            <br/>
            E
            <br/>
            M
            <br/>
            P
            <br/>
            .
           </span>
           <br/>
           <br/>
           <span id="lastrachas">
            G
            <br/>
            U
            <br/>
            S
            <br/>
            T
           </span>
           <br/>
           <br/>
           <span id="lastwind">
            W
            <br/>
            I
            <br/>
            N
            <br/>
            D
           </span>
           <br/>
           <br/>
           <span id="lastrainleged">
            R
            <br/>
            A
            <br/>
            I
            <br/>
            N
           </span>
          </div>
          <canvas id="last6hours">
          </canvas>
         </div>
         <div class="chart-containerw">
          <canvas id="last6hourswind2">
          </canvas>
         </div>
         <div class="chart-containerw">
          <canvas id="last6hourswind">
          </canvas>
         </div>
         <div id="rainchart">
         </div>
        </div>
       </div>
      </div>
      <h2 class="textcam">
       Weather forecast for Málaga
      </h2>
      <div id="predicciontage2">
      </div>
      <div style="clear: both;">
      </div>
      <div class="menu-wrapper2" id="wettertab">
       <ul class="menuwetter">
        <li class="item" id="item0">
        </li>
        <!--
-->
        <li class="item" id="item1">
        </li>
        <!--
-->
        <li class="item" id="item2">
        </li>
        <!--
-->
        <li class="item" id="item3">
        </li>
        <!--
-->
        <li class="item" id="item4">
        </li>
        <!--
-->
        <li class="item" id="item5">
        </li>
       </ul>
       <div id="tempfix">
        <label>
         TEMPERATURE:
        </label>
        <select id="gradselect">
         <option value="celsius">
          Celsius
         </option>
         <option value="fahrenheit">
          Fahrenheit
         </option>
        </select>
       </div>
       <div id="wfix">
        WIND DIRECTION &amp; SPEED
        <select id="laenge">
         <option value="kmh">
          km/h
         </option>
         <option value="kn">
          kn
         </option>
         <option value="mph">
          mph
         </option>
         <option value="ms">
          m/s
         </option>
         <option value="fts">
          ft/s
         </option>
        </select>
       </div>
       <div id="befix">
        BEAUFORT
       </div>
       <div id="bfix">
        GUSTS
        <span id="einheitboe">
        </span>
       </div>
       <div id="nfix">
        <label>
         PRECIPITATION:
        </label>
        <select id="regenmenge">
         <option value="mm/h">
          mm/h
         </option>
         <option value="inch">
          inch
         </option>
        </select>
       </div>
       <div id="wfix1">
        <label>
         HEIGHT OF WAVES
        </label>
        <select id="wellenhoehe">
         <option value="m">
          m
         </option>
         <option value="ft">
          ft
         </option>
        </select>
       </div>
       <div id="wfix2">
        DIRECTION OF WAVES
       </div>
       <div id="wfix3">
        WAVE PERIOD (s)
       </div>
       <div class="paddles" id="padd">
        <button class="left-paddle paddle hidden">
         &lt;
        </button>
        <button class="right-paddle paddle">
         &gt;
        </button>
       </div>
      </div>
     </div>
     <!-- END contentcams -->
     <div id="sidebarcams">
      <span id="werbung">
      </span>
      <div id="werbebanner-resp2">
       <aside class="responsive-banner2">
        <a href="https://meteo365.es/weather/spain/observation/" target="_blank">
         <div class="container-envelope">
          <svg class="cirle-c" height="600" width="600">
           <circle cx="300" cy="300" r="300">
           </circle>
          </svg>
          <img src="https://meteo365.es/wetter/images/banner_obs_temp.png">
           <div class="col-xs-12">
            <a class="more-link" href="https://meteo365.es/weather/spain/observation/" target="_blank">
             Click here
            </a>
            <p>
             Weather observations SPAIN
            </p>
           </div>
          </img>
         </div>
        </a>
       </aside>
      </div>
      <div id="metarmitte222">
       <div id="martemp1a">
        <small>
         WATER TEMPERATURE
        </small>
        <div class="watertemperatur">
         <img class="watertemp" src="https://meteo365.es/livecams/images/watertemp.png"/>
        </div>
        <div id="wassertemp">
        </div>
       </div>
       <div id="uves2a">
        <div class="uvtext">
         Max. UV Index today in Málaga:
         <span id="uvheute">
         </span>
        </div>
        <div id="rundumgraph">
         <canvas height="180" id="UVTODAY" width="180px">
         </canvas>
        </div>
       </div>
       <div class="cardsun">
        <sun-card id="test">
        </sun-card>
       </div>
      </div>
     </div>
     <!-- END sidebar -->
     <br/>
     <div style="clear: both;">
     </div>
     <div id="daten">
      Weather forecast based on ICON-EU and EWAM models - data prepared by © meteo365.es
     </div>
     <script>
      var uvdataheute = "10";
document.getElementById("uvheute").innerHTML = "<strong>"+uvdataheute+"</strong>";
              
 var wth = "23"; var resfah = wth *(9/5) +32;
document.getElementById("wassertemp").innerHTML = "<strong>"+wth+"</strong> °C <br> <strong>"+parseFloat(resfah).toFixed(0)+"</strong> °F";  
var opts = {
  angle: -0.2, // The span of the gauge arc
  lineWidth: 0.2, // The line thickness
  radiusScale: 0.75, // Relative radius
  pointer: {
    length: 0.56, // // Relative to gauge radius
    strokeWidth: 0.02, // The thickness
    color: '#000000' // Fill color
  },
  limitMax: true,     // If false, max value increases automatically if value > maxValue
  limitMin: true,     // If true, the min value of the gauge will be fixed
  colorStart: '#6F6EA0',   // Colors
  colorStop: '#C0C0DB',    // just experiment with them
  strokeColor: '#EEEEEE',  // to see which ones work best for you
  highDpiSupport: true,     // High resolution support
  percentColors: [[0.0, "#a9d70b" ], [0.50, "#f9c802"], [1.0, "#ff0000"]], // !!!!
  strokeColor: '#E0E0E0',
  generateGradient: true ,
    staticZones: [
   {strokeStyle: "#9cc401", min: 1, max: 2.5}, // Red from 100 to 130
   {strokeStyle: "#ffba03", min: 2.5, max: 5.5}, // Yellow
   {strokeStyle: "#f78600", min: 5.5, max: 7.5}, // Green
   {strokeStyle: "#f54f25", min: 7.5, max: 10.5}, // Yellow
   {strokeStyle: "#8b52bd", min: 10.5, max: 11}  // Red
],
    staticLabels: {
  font: "10px sans-serif",  // Specifies font
  labels: [1,2,3,4,5,6,7,8,9,10,11],  // Print labels at these values
  color: "#fff",  // Optional: Label text color
  fractionDigits: 0  // Optional: Numerical precision. 0=round off.
},
  
};
var target = document.getElementById('UVTODAY'); // your canvas element
var gauge = new Gauge(target).setOptions(opts); // create sexy gauge!
gauge.maxValue = 12; // set max gauge value
gauge.setMinValue(0);  // Prefer setter over gauge.minValue = 0
gauge.animationSpeed = 63; // set animation speed (32 is default value)
gauge.set(uvdataheute); // set actual value
   
    var sunr = "2025-07-16T07:14:41+02:00";
    var suns = "2025-07-16T21:39:44+02:00";
    var trans = "2025-07-16T14:27:12+02:00";
    var dawn = "2025-07-16T06:46:38+02:00";
    var dusk = "2025-07-16T22:07:47+02:00";

    
  setTimeout(function(){     
    const darkMode = true
    if (!darkMode) {
      document.body.classList.add('light')
    }

    
    var test = document.querySelector('#test')
    test.setConfig({
      title: 'Sunrise & Sunset'
    })
    test.hass = {
      language: 'en',
      locale: {
        language: 'en'
      },
      themes: { darkMode },
      states: {
        'sun.sun': {
          state: 'below_horizon',
          attributes: {
            azimuth: 5.01,
            elevation: -19.15,
            next_dawn: dawn,
            next_dusk: dusk,
            next_midnight: '2021-05-16T23:57:08+00:00',
            next_noon: trans,
            next_rising: sunr,
            next_setting: suns,
            rising: true
          }
        }
      }
    }
}, 1100);
     </script>
     <svg height="0" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <defs>
       <filter color-interpolation-filters="sRGB" id="bwFilter">
        <!-- Convert to grayscale based on luminance -->
        <fecolormatrix type="matrix" values="0.2126 0.7152 0.0722 0 0
                        0.2126 0.7152 0.0722 0 0
                        0.2126 0.7152 0.0722 0 0
                        0 0 0 1 0">
        </fecolormatrix>
        <!-- Expand edges slightly to clean up any fringing -->
        <femorphology operator="dilate" radius="2">
        </femorphology>
        <!-- Apply the threshold to determine if the color should be black or white -->
        <fecomponenttransfer>
         <fefuncr intercept="128" slope="-255" type="linear">
         </fefuncr>
         <fefuncg intercept="128" slope="-255" type="linear">
         </fefuncg>
         <fefuncb intercept="128" slope="-255" type="linear">
         </fefuncb>
        </fecomponenttransfer>
        <!-- Composite step to clean up the result -->
        <fecomposite in2="SourceGraphic" operator="in">
        </fecomposite>
       </filter>
      </defs>
     </svg>
     <script>
      var scrollDuration = 300;
var leftPaddle = document.getElementsByClassName('left-paddle');
var rightPaddle = document.getElementsByClassName('right-paddle');
var itemsLength = $('.item').length;
var itemSize = $('.item').outerWidth(true);
var paddleMargin = 20;

var getMenuWrapperSize = function() {
	return $('.menu-wrapper2').outerWidth();
}
var menuWrapperSize = getMenuWrapperSize();
$(window).on('resize', function() {
	menuWrapperSize = getMenuWrapperSize();
});
var menuVisibleSize = menuWrapperSize;

var getMenuSize = function() {
	return itemsLength * itemSize;
};
var menuSize = getMenuSize();
var menuInvisibleSize = menuSize - menuWrapperSize;

var getMenuPosition = function() {
	return $('.menuwetter').scrollLeft();
};

$('.menuwetter').on('scroll', function() {
    
	menuInvisibleSize = menuSize - menuWrapperSize;
	var menuPosition = getMenuPosition();
     checkscroll(menuPosition);

	var menuEndOffset = menuInvisibleSize - paddleMargin;

	if (menuPosition <= paddleMargin) {
		$(leftPaddle).addClass('hidden');
		$(rightPaddle).removeClass('hidden');
	} else if (menuPosition < menuEndOffset) {
		// show both paddles in the middle
		$(leftPaddle).removeClass('hidden');
		$(rightPaddle).removeClass('hidden');
	} else if (menuPosition >= menuEndOffset) {
		$(leftPaddle).removeClass('hidden');
		$(rightPaddle).addClass('hidden');
}


});

$(rightPaddle).on('click', function() {
	$('.menuwetter').animate( { scrollLeft: '+=100px'}, scrollDuration);
});

$(leftPaddle).on('click', function() {
	$('.menuwetter').animate( { scrollLeft: '0' }, scrollDuration);
});
    
        
        
        
        
function setCookie(cname, cvalue, exdays) {
  const d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  let expires = "expires="+d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

function resetCookie() { 
  const d = new Date();
  d.setTime(d.getTime() + (365 * 24 * 60 * 60 * 1000));
  let expires = "expires="+d.toUTCString();
var t1 = document.getElementById('laenge').value;
var t2 = document.getElementById('gradselect').value;	
var t3 = document.getElementById('regenmenge').value;     
var t4 = document.getElementById('wellenhoehe').value;
var cvalue = t1+","+t2+","+t3+","+t4; 
document.cookie = "einheiten" + "=" + cvalue + ";" + expires + ";path=/";
}   
    
function getCookie(cName) {
      const name = cName + "=";
      const cDecoded = decodeURIComponent(document.cookie); console.log("cDecoded: "+cDecoded);
      const cArr = cDecoded.split('; ');
      let res;
      cArr.forEach(val => {
          if (val.indexOf(name) === 0) res = val.substring(name.length);
      })
      return res;
}

function checkCookie() {
 let user = getCookie("einheiten");
 setCookie("einheiten", user, 365); 
}  
     
    
    
var xy = getCookie("einheiten");
    if(xy === undefined){ resetCookie(); var xy = getCookie("einheiten"); }
var einheitenwechsel = xy.split(",");    
var speed = einheitenwechsel[0];
var grad = einheitenwechsel[1];
var liter = einheitenwechsel[2];
var hoehe = einheitenwechsel[3];
var tempeinheit, gradeinheit, regeneinheit;
    
document.getElementById('laenge').value = speed;
document.getElementById("gradselect").value = grad;	
document.getElementById('regenmenge').value = liter;   
document.getElementById('wellenhoehe').value = hoehe; 
    
   
  
     
function tempcolor(val) {
    
if (val < -30) { return "109, 50, 125"; } else 
if (val < -20) { return "122, 56, 140"; } else 
if (val < -15) { return "151, 48, 141"; } else 
if (val < -10) { return "180, 40, 141"; } else 
if (val < -8) { return "209, 33, 142"; } else 
if (val < -6) { return "137, 43, 226"; } else 
if (val < -4) { return "167, 96, 233"; } else 
if (val < -2) { return "196, 149, 240"; } else 
if (val < 0) { return "225, 201, 247"; } else 
if (val < 2) { return "255, 255, 255"; } else 
if (val < 4) { return "26, 26, 112"; } else 
if (val < 6) { return "26, 54, 148"; } else 
if (val < 8) { return "28 ,84, 184"; } else 
if (val < 10) { return "22, 90, 176"; } else 
if (val < 12) { return "31, 143, 255"; } else 
if (val < 14) { return "0, 237, 237"; } else 
if (val < 16) { return "10, 217, 214"; } else 
if (val < 18) { return "20, 196, 191"; } else 
if (val < 20) { return "26, 143, 137"; } else 
if (val < 22) { return "102, 255, 102"; } else 
if (val < 24) { return "204, 255, 0"; } else 
if (val < 26) { return "255, 255, 0"; } else 
if (val < 28) { return "255, 191, 0"; } else 
if (val < 30) { return "255, 158, 0"; } else 
if (val < 32) { return "255, 128, 0"; } else 
if (val < 34) { return "227, 115, 2"; } else 
if (val < 36) { return "255, 0, 0"; } else 
if (val < 38) { return "255, 51, 178"; } else 
if (val < 40) { return "232, 51, 145"; } else 
if (val < 42) { return "209, 51, 112"; } else 
if (val < 44) { return "178, 51, 79"; } else 
if (val < 46) { return "161, 54, 46"; } else 
if (val < 48) { return "123, 13, 36"; } else 
if (val < 50) { return "91, 12, 29"; } else 
if (val < 54) { return "87, 5, 23"; } else 
if (val >= 54) { return "39, 2, 55"; }  
     
}       
      
      
  function degToCompassshort(num) {
    var val = Math.floor((num / 22.5) + 0.5);
    var arr = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE", "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"];
    return arr[(val % 16)];
}    
      
    var winds = [ 0, 2, 6, 12, 20, 29, 39, 50, 62, 75, 89, 103, 118 ];
	var langs = {
		'en' : ['Calm', 'Light air', 'Light breeze', 'Gentle breeze', 'Moderate breeze', 'Fresh breeze', 'Strong breeze', 'High wind', 'Gale', 'Strong gale', 'Storm', 'Violent Storm', 'Hurricane'],
        'de' : ['Windstille', 'leiser Zug', 'leichte Brise', 'schwache Brise', 'mäßige Brise', 'frische Brise', 'starker Wind', 'steifer Wind', 'stürmischer Wind', 'Sturm', 'schwerer Sturm', 'orkanartiger Sturm', 'Orkan'],
		'es' : ['Calma', 'Ventolina', 'Brisa muy débil', 'Brisa ligera', 'Brisa moderada', 'Brisa fresca', 'Brisa fuerte', 'Viento fuerte', 'Temporal', 'Temporal fuerte', 'Temporal duro', 'Borrasca', 'Huracán']
	};
      
      var colors= ["119, 202, 237", "0, 105, 180", "0, 72, 153", "0, 73, 127", "12, 130, 130", "81, 170, 86", "249, 208, 87", "255, 151, 0", "255, 0, 0", "210, 51, 111","184, 16, 57","91, 12, 29", "55, 4, 15"];

	var remap = function(range_from, range_to, value) {
		var v = Math.floor(((value - range_from)*100) / (range_to - range_from)) / 100;
		return (v<0) ? 0 : v;
	};

	var beaufort = function(speed, options) {

		var opts = options || {};

			if ( typeof opts.lang == 'object' ) {
				langs.arr = options.lang;
				opts.lang = 'arr';
			}

		var settings = {
			lang: opts.lang || 'en',
			int: opts.int || false
		};

		var grades = [];
		for ( var i = 0; i < winds.length; i++ ) {
			grades.push( { speed : winds[i], desc : langs[settings.lang][i] } );
		}

		var grade = false;
		grades.forEach(function(el, i) {
			if ( el.speed > speed && !grade ) {
				grade = i;
			}
		});
		grade = (grade) ? (grade-1) : (grades.length-1);
		var data = {
			desc : grades[grade].desc,
            color : colors[grade],
			grade : ((settings.int) ? grade : (grade + remap(grades[grade].speed, ((grades[grade+1]) ? grades[grade+1].speed : false), speed)))
		};
		return data;
	};

    if (typeof define === 'function' && define.amd) {
        define(function () { return beaufort; });
    } else if (typeof exports !== 'undefined') {
        if (typeof module !== 'undefined' && module.exports) {
            exports = module.exports = beaufort;
        }
        exports.beaufort = beaufort;
    } else {
        //global.beaufort = beaufort;
    }      
      
      
      
var name = "malaga";
    
var timeoffset;  var zeitzone="Europe/Madrid";   var resultsun;
var lon ="-5.26985"; var lat="36.28849";   
    
    
    

function fetchdatazu() {

function sunfrage(lon,lat,zeitzone) { 
resultsun = $.ajax({
    type: 'POST',
	crossDomain: true,
    async: false,   
    url: 'puertos/sun.php',
    data: ({
        lon: lon,
        lat: lat,
        zeitzone: zeitzone
    })
}).responseText; 	
return resultsun;
}	
sunfrage(lon,lat,zeitzone);
    
timeoffset = "7200";
   
   var exswert = new Date();
readTextFile("prediccion/"+name+".json?t="+exswert);    
   
}
  
fetchdatazu();
  
   
 $( "#gradselect" ).change(function() { grad = document.getElementById("gradselect").value; resetCookie(); fetchdatazu(); Charttemp.destroy(); Chartwind.destroy(); Chartwind2.destroy(); station(); var menuPosition = getMenuPosition();  checkscroll(menuPosition); })
 $( "#regenmenge" ).change(function() { liter = document.getElementById("regenmenge").value; resetCookie(); fetchdatazu(); Charttemp.destroy(); Chartwind.destroy(); Chartwind2.destroy(); station(); var menuPosition = getMenuPosition();  checkscroll(menuPosition); })
 $( "#laenge" ).change(function() { speed = document.getElementById("laenge").value; resetCookie(); fetchdatazu(); Charttemp.destroy(); Chartwind.destroy(); Chartwind2.destroy(); station(); var menuPosition = getMenuPosition();  checkscroll(menuPosition); })
 $( "#wellenhoehe" ).change(function() { hohe = document.getElementById("wellenhoehe").value; resetCookie(); fetchdatazu(); var menuPosition = getMenuPosition();  checkscroll(menuPosition); })
   
    
function readTextFile(file)
{
    var rawFile = new XMLHttpRequest();
    rawFile.open("GET", file, false);
    rawFile.onreadystatechange = function ()
    {
        if(rawFile.readyState === 4)
        {
            if(rawFile.status === 200 || rawFile.status == 0)
            {
                var allText = rawFile.responseText;
                var myArray = allText.split(/\r?\n/);
                var zeituv22 = myArray[1]; var uhrzeit = zeituv22.split(", "); 
                var alltemps = myArray[2].split("temp:"); var temperatur = alltemps[1].split(","); 
                var allgusts = myArray[3].split("boe:"); var boen = allgusts[1].split(","); 
                var allwds = myArray[4].split("windgeschwindigkeit:"); var ws = allwds[1].split(","); 
                var rainpz44 = myArray[5].split("regen:"); var regen = rainpz44[1].split(","); 
                var snowio99 = myArray[6].split("schnee:"); var schnee = snowio99[1].split(","); 
                var ertw = myArray[7].split("wetter:"); var icons = ertw[1].split(","); 
                var sdfsdc = myArray[8].split("richtung:"); var wr = sdfsdc[1].split(","); 

                var firsthora = uhrzeit[0].split(" "); 
                 
               function zweistellig (s) { while (s.toString().length < 2) {s = "0" + s;} return s; }	
               function einstellig(s) { const firstDigitStr = String(s)[0]; const second = String(s)[1]; if(firstDigitStr==0) { s = second; } return s; }
                
                
                var now = new Date; const utch = now.getUTCHours(); const utcday = now.getUTCDate(); const utcmo = now.getUTCMonth()+1; const utcyear = now.getUTCFullYear();
                //const neuesdatum = parseInt(utcday)+"."+utcmo+"."+utcyear+" "+utch;
                const neuesdatum = utcyear+"-"+zweistellig(utcmo)+"-"+zweistellig(utcday)+" "+zweistellig(utch)+":00:00";

                const greaterThan = uhrzeit.indexOf(neuesdatum);
                const greaterThanTen = greaterThan; //+1
                
                
                let minutes = (parseFloat(timeoffset) / (1 * 60)).toFixed(0);
                
                let result = minutes.includes("-");
                //var end12horas = greaterThanTen+12;
                var end12horas = uhrzeit.length-1;
                var wievielehorasnoch=23-greaterThanTen;
              
                var horazeitenhourly  = zeituv22;
                var uhrzeitneud = horazeitenhourly.split(", ");
                var neueuhrzeiten=[];
                for (var i=0;i<uhrzeitneud.length;i++) { 
                var tec = uhrzeitneud[i].replaceAll("-","/");
                var dt = new Date(tec); dt.setHours(dt.getHours() + horoai);
                var clock = dt.toLocaleString('de-DE', {day:"2-digit", month:"2-digit", year:"numeric", hour:"2-digit", minute:"2-digit"}); 
                   neueuhrzeiten.push(clock);
                }
                
                var lb0, lb1, lb2, lb3, lb4, lb5, lb6;
                var lb0a, la1, la2, la3, la4, la5, la6;
                
                var horasnoche = resultsun.split(",");

                var horayaa=[];
                for(var p=0;p<uhrzeitneud.length;p++) {
                    var uhrzeiop = neueuhrzeiten[p].split(".");
                    horayaa.push(uhrzeiop[0]);
                }
                const counts = {};
                
                horayaa.forEach(function (x) { counts[x] = (counts[x] || 0) + 1; });
                //const propOwn = Object.getOwnPropertyNames(counts);
                //propOwn.sort();
                
                function uniqueArray(value, index, self) 
                { 
                    return self.indexOf(value) === index;
                }

                const propOwn = horayaa.filter(uniqueArray);
                
                
                 console.log("propOwn.length: "+propOwn + " " + propOwn.length);
                //propOwn.sort();
                
                var arraycount=[]; var arraycount2=[];
                
                //var keys = Object.keys(counts),
                var keys = propOwn,    
                i, len = keys.length;
                console.log("keys: "+keys );
                //keys.sort();

                for (i = 0; i < len; i++) {
                    k = keys[i];
                    arraycount.push(k); arraycount2.push(counts[k]);
                    console.log(k + ':' + counts[k]);
                }
                var now = new Date; const utcdayd = now.getDate(); const utchourd = now.getHours();
                //var arraycount = Object.keys(counts);
                //var arraycount2 = Object.values(counts); 
                var zb=arraycount2[0]; var line="";
                
  //               var tagliste="";
                
  //               if(arraycount[0]==utcdayd && utchourd<=11) { var begin=0; var ende=zb; for(var z=0;z<zb;z++) { line+= uhrzeitneud[z]+"<br>"; }
 //                var dia = uhrzeitneud[0].split(" "); const datumr = new Date(); var datumrechnen = datumr.toLocaleString('de-DE', {timeZone: "UTC", weekday:"long", day:"2-digit", month:"2-digit", year:"numeric"}); lb0="0"; lb0a=zb;  tagliste+="<div class='tagleft' id='ab0'>"+datumrechnen+"</div>";
                    
 //                } else { var begin=zb; var ende=arraycount2[1]+zb;   }
                
                var arrayuhrzeiten=[]; arrayuhrzeiten.push(zb); 
    //            for(var m=1;m<propOwn.length;m++) {
    //                
  //                   if(arraycount2[m] >= 6) { var zb2=zb+arraycount2[m]; var hora = neueuhrzeiten[zb].split(', '); var parts = hora[0].split('.'); 
//                                                                  var horaeinuhr = parts[2]+"-"+zweistellig(parts[1])+"-"+zweistellig(parts[0])+"T"+zweistellig(hora[1])+":00Z";   
//                                                                  var dt = new Date(horaeinuhr); var datumrechnen = dt.toLocaleString('de-DE', {timeZone: "UTC", weekday:"long", day:"2-digit", month:"2-digit", year:"numeric"}); var dia = uhrzeitneud[zb].split(" "); var classid="ab"+m; if(m==1){la1=zb;lb1=zb2;} else if(m==2){la2=zb;lb2=zb2;} else if(m==3){la3=zb;lb3=zb2;} else if(m==4){la4=zb;lb4=zb2;} else if(m==5){la5=zb;lb5=zb2;} else if(m==6){la6=zb;lb6=zb2;} tagliste+="<div class='tagleft' id='"+classid+"'>"+datumrechnen+"</div>"; }
//                      zb=zb+arraycount2[m];
//                 }
                
                
                
                var windeinstellung = document.getElementById('laenge').value; var einheit;
        if(windeinstellung=="kn") {	 einheit = "kn"; } else if (windeinstellung=="ms") { einheit = "m/s";  } else if (windeinstellung=="mph") { einheit = "mph";  } else if (windeinstellung=="fts") { einheit = "ft/s";  } else { einheit = "km/h"; }
document.getElementById('einheitboe').innerHTML = "("+einheit+")";
                  
        var tempeinheit = document.getElementById('gradselect').value;	var gradeinheit; 
        if(tempeinheit=="fahrenheit") { gradeinheit = "°F"; } else { gradeinheit = "°C"; }      
                
           var valuerain = document.getElementById('regenmenge').value; var regeneinheit;
                
        function windgeschwindigkeit(result) {
     if(windeinstellung=="kn") { var menge = result/1.852; } 
else if (windeinstellung=="ms") { var menge = result/3.6; }								
else if (windeinstellung=="mph") { var menge = result*0.621; }								
else if (windeinstellung=="fts") { var menge = result*0.91; }								
            else {	var menge = result;}

            return menge.toFixed(0);	
             }        
                
                
            
        function tempausgabe(result) {
            if(tempeinheit=="fahrenheit") { var menge = result *(9/5) +32 } else { var menge = result; }  
            
            return parseFloat(menge).toFixed(0);
        }        
                
        function regenausgabe(result) { if (valuerain=="inch") { var menge = result/25.4;} else { var menge = result; }     
                
        return parseFloat(menge).toFixed(2);
        }   
                
                
                
                
                
                
                
               
                
                
                
                
                
                
                const datumfueruhrzeit = new Date(); var uhrzeitvorort2 = datumfueruhrzeit.toLocaleString('de-DE', {timeZone: "Europe/Madrid", hour:"2-digit"}); var uhrzeitvorort = uhrzeitvorort2.replace(" Uhr", "");
                const datumr = new Date(); var datumrechnen = datumr.toLocaleString('de-DE', {timeZone: "Europe/Madrid", day:"2-digit", month:"2-digit", year:"numeric"}); 
                var datumrechnenutc = datumr.toLocaleString('de-DE', { timeZone: "UTC", day:"2-digit", month:"2-digit", year:"numeric"});
                var whichday; 
                if (datumrechnen == datumrechnenutc) { whichday = "0";} else if (datumrechnen > datumrechnenutc) { whichday = "1";} else if (datumrechnen < datumrechnenutc) { whichday = "2";}
                
                 var windeinstellung = document.getElementById('laenge').value; var einheit;
        if(windeinstellung=="kn") {	 einheit = "kn"; } else if (windeinstellung=="ms") { einheit = "m/s";  } else if (windeinstellung=="mph") { einheit = "mph";  } else if (windeinstellung=="fts") { einheit = "ft/s";  } else { einheit = "km/h"; }
document.getElementById('einheitboe').innerHTML = "("+einheit+")";
                  
        var tempeinheit = document.getElementById('gradselect').value;	var gradeinheit; 
        if(tempeinheit=="fahrenheit") { gradeinheit = "°F"; } else { gradeinheit = "°C"; }      
                
           var valuerain = document.getElementById('regenmenge').value; var regeneinheit;
                
        function windgeschwindigkeit(result) {
     if(windeinstellung=="kn") { var menge = result/1.852; } 
else if (windeinstellung=="ms") { var menge = result/3.6; }								
else if (windeinstellung=="mph") { var menge = result*0.621; }								
else if (windeinstellung=="fts") { var menge = result*0.91; }								
            else {	var menge = result;}

            return menge.toFixed(0);	
             }        
                
            
        function tempausgabe(result) {
            if(tempeinheit=="fahrenheit") { var menge = result *(9/5) +32 } else { var menge = result; }  
            
            return parseFloat(menge).toFixed(0);
        }        
                
        function regenausgabe(result) { if (valuerain=="inch") { var menge = result/25.4;} else { var menge = result; }     
                
        return parseFloat(menge).toFixed(2);
        }   
                

                var iconbild = icons[greaterThanTen-1]; if(iconbild=="empty") { iconbild=icons[greaterThanTen-2]; } if(iconbild=="empty") { iconbild=icons[greaterThanTen+1]; }                
                
                var horasnoche = resultsun.split(",");
                var horas = [];  var tagnamen = []; var anzahl = []; var p = 0;
                
                
                var table=""; for (var i=greaterThanTen;i<end12horas;i++) { var bile = uhrzeit[i].split(' '); var parts = bile[0].split('-'); var hora = bile[1]; 
                    var horaeinuhr = bile[0]+"T"+zweistellig(bile[1])+"Z";       
                    var dt = new Date(horaeinuhr);   dt.setMinutes(dt.getMinutes()); 
                    table += "<td class='uhrzeit'>" + dt.toLocaleString('de-DE', {hour:"2-digit", minute:"2-digit"}) + "</td>"; horas.push(dt.toLocaleString('de-DE', {hour:"2-digit"})); tagnamen.push(dt.toLocaleString('de-DE', {weekday:"long"})); }
                
                const countsT = {};
                tagnamen.forEach(function (x) { countsT[x] = (countsT[x] || 0) + 1; });
                const propOwnT = Object.getOwnPropertyNames(countsT);
                
                var tabauf="";
                for(key in countsT) {
                    if(countsT.hasOwnProperty(key)) {
                        var value = countsT[key];
                        anzahl.push(value);
                    }
                }
                
                var zahl1=greaterThanTen; var zahl2; var tablewetterdia0;
                
                if(anzahl.length==5) { document.getElementById("item5").style.width="180px"; } else {document.getElementById("item5").style.marginRight="180px";}
                
                
               for(var q=0;q<anzahl.length;q++) { 
               
                   zahl2=zahl1+anzahl[q];
                   
                  tablewetterdia0="";
                   
                   if(propOwnT[q]=="Montag") { var tagee="Monday";} else if(propOwnT[q]=="Dienstag") { var tagee="Tuesday";} else if(propOwnT[q]=="Mittwoch") { var tagee="Wednesday";} else if(propOwnT[q]=="Donnerstag") { var tagee="Thursday";} else if(propOwnT[q]=="Freitag") { var tagee="Friday";} else if(propOwnT[q]=="Samstag") { var tagee="Saturday";} else if(propOwnT[q]=="Sonntag") { var tagee="Sunday";}
                   tablewetterdia0="<div class='sticky-header'>"+tagee+"</div><table class='wetter'><tr>";
                   for (var i=zahl1;i<zahl2;i++) { var bile = uhrzeit[i].split(' '); var parts = bile[0].split('-'); var hora = bile[1]; 
                    var horaeinuhr = bile[0]+"T"+zweistellig(bile[1])+"Z";    
                    var dt = new Date(horaeinuhr);   dt.setMinutes(dt.getMinutes()); 
                    tablewetterdia0+="<td class='uhrzeit'>" + dt.toLocaleString('de-DE', {hour:"2-digit", minute:"2-digit"}) + "</td>"; }

                   tablewetterdia0+="</tr><tr>";
                   
                   for (var i=zahl1;i<zahl2;i++) { if (icons[i]=="empty") { if (icons[i+1]=="empty") { var pic = iconbild;} else {var pic = icons[i+1];} } else { var pic = icons[i]; } iconbild = pic; var bbib=pic.split('.');
                     if(horas[p] > horasnoche[1]+1 || horas[p] <= horasnoche[0]) {tablewetterdia0 += "<td><img src='https://meteo365.es/wetter/wettericons/" + bbib[0] + "n.png' width='36' /></td>"; }  else { tablewetterdia0 += "<td><img src='https://meteo365.es/wetter/wettericons/" + bbib[0] + ".png' width='36' /></td>";} p = p+1; }

                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+anzahl[q]+"'></td></tr><tr>";
                   
                  for (var i=zahl1;i<zahl2;i++) { var camp = parseFloat(temperatur[i]).toFixed(0); var tm = tempausgabe(camp); if(tm=="-0") { var tt = "0";} else { var tt = tm; } tablewetterdia0 += "<td style='background-color:rgb("+tempcolor(camp)+")'><span class='tbg' style='color:rgb("+tempcolor(camp)+"); filter: url(#bwFilter);'>" + tt + " "+gradeinheit+"</span></td>"; }  

                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+anzahl[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { var wricht = wr[i].split("."); var speed = ws[i]; tablewetterdia0 += "<td class='wind'><div style='transform: rotate("+wricht[0]+"deg);'>&darr;</div><div style='display:inline; font-size:11px;'>" + degToCompassshort(wricht[0]) +" </div><div>"+ windgeschwindigkeit(parseFloat(speed)) + "</div></td>";  } 
                   
                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+anzahl[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { var speed = ws[i].split("."); var scala = beaufort(speed[0], {lang: 'de', int: true}); var color = scala["color"]; var stark = scala["grade"]; tablewetterdia0 += "<td><span class='bfscalac2' style='background-color:rgb("+color+");'>"+stark+"</span></td>";  } 


                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+anzahl[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { var boe = boen[i].split("."); var racha = parseFloat(boe[0]); if(racha>="63") { var gust = "<span class='boe'>"+windgeschwindigkeit(racha)+"</span>";} else { var gust = windgeschwindigkeit(racha);} tablewetterdia0 += "<td  class='wind'>" + gust + " </td>"; } 
                   
                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+anzahl[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { var lluvia = parseFloat(regen[i]).toFixed(2);  var nieve = parseFloat(schnee[i]).toFixed(2); if( lluvia>=nieve ) { if(lluvia>="0.02") { tablewetterdia0 += "<td>" + regenausgabe(lluvia) + "</td>"; } else { tablewetterdia0 += "<td>0</td>"; } } else { if(nieve>="0.02") { tablewetterdia0 += "<td>" + nieve + "</td>"; } else { tablewetterdia0 += "<td>0</td>"; } } } 
                   
                   tablewetterdia0+="</tr></table>";
                   
                   var div="item"+q;
                   var tabe = "85"*anzahl[q];
                   document.getElementById(div).style.width=tabe+"px";
                   document.getElementById(div).innerHTML=tablewetterdia0;
                   
                    zahl1=zahl1+anzahl[q];
               
               
               }
                

                readTextFile22(anzahl); 
   
                
                
                
                
                var predtage=[];var predtmax=[];var predtmin=[];var predicon=[];var predrain=[];var predsnow=[];var predpmax=[];var predpmin=[];var predwsmax=[];var predwsmin=[];var predboemax=[];
                var preda1=[]; var preda2=[]; var preda3=[]; var preda4=[];
                
                
                
//var arraycount = Object.keys(counts);
//var arraycount2 = Object.values(counts);
//var zb=arraycount2[0];
                
var array06=[]; var array612=[]; var array1218=[]; var array1824=[];                
function fcielo(val,img) {  
if (val < 6) { array06.push(img); } else 
if (val >= 6 && val < 12) { array612.push(img); } else 
if (val >= 12 && val < 18) { array1218.push(img); } else { array1824.push(img); }  
}               
                
                
                function dia0() { const datumr = new Date(); var datumrechnen = datumr.toLocaleString('de-DE', { day:"2-digit", month:"2-digit"});
                var temp = []; var rain = parseFloat(0); var snow = 0; var cielo = []; var presion = []; var wspeed = []; var boens = []; var humi = [];  
                  for(var i=0;i<zb;i++) {  var slice = neueuhrzeiten[i].slice(12, 14); 
                                            if(slice>=utchourd) {
                                                var tgm = parseFloat(temperatur[i]).toFixed(0); if (tgm=="-0") { temp.push("0"); } else { temp.push(tgm); }
                                                if (icons[i]!="empty") { var numb = icons[i].match(/\d/g); numb = numb.join(""); cielo.push(numb); }  
                                                var lluvia = parseFloat(regen[i]).toFixed(2); if(lluvia>0) { rain = parseFloat(rain) + parseFloat(lluvia); } 
                                                var nieve = parseFloat(schnee[i]).toFixed(2); if(nieve>0) { snow =  parseFloat(snow) + parseFloat(nieve); }
                                                
                                                var speed = parseFloat(ws[i]); wspeed.push(speed); 
                                                var boe = parseFloat(boen[i]); boens.push(boe);                                                 
                                                                                               
                                                
                                                fcielo(slice,numb); 
                                            }
                                        
                                        }                   
                    var upside = temp.sort(function(a, b){return a-b});  var himmel = cielo.sort(function(a, b){return a-b});
                    var zahl = upside.length-1; var zahlcie = himmel.length-1; var tmax = upside[zahl]; var tmin = upside[0]; var heaven = himmel[zahlcie];
                    var uniqueList = Array.from(new Set(cielo));  var lang = uniqueList.length-1;   var newicon =heaven;           
                    if(uniqueList[lang]>=80&&uniqueList[lang]<=86) { if(uniqueList[lang-1]>=61) { var newicon = uniqueList[lang-1]; } else { var newicon = uniqueList[lang]; }  }
                    
                    var sortwind = wspeed.sort(function(a, b){return a-b}); var wsmax = windgeschwindigkeit(sortwind[zahl]); var wsmin = windgeschwindigkeit(sortwind[0]);
                    var sortboe = boens.sort(function(a, b){return a-b}); var boemax = windgeschwindigkeit(sortboe[zahl]); 
                     var datumrechnen = dt.toLocaleString('de-DE', {timeZone: "UTC", weekday:"short", day: "numeric", month: "long"});
                      predtage.push("Today");    //predtage.push(datumrechnen);       
                     predtmax.push(tmax); predtmin.push(tmin); predicon.push(newicon); predrain.push(rain); predsnow.push(snow);  predwsmax.push(wsmax); predwsmin.push(wsmin); predboemax.push(boemax);  
                                 
                                if(array06.length == 0) { var a1 = "null";  } else { var a1 = mode(array06); }
                                if(array612.length == 0) { var a2 = "null";  } else { var a2 = mode(array612); } 
                                if(array1218.length == 0) { var a3 = "null";  } else { var a3 = mode(array1218); } 
                      var a4 = mode(array1824);             
                      preda1.push(a1); preda2.push(a2); preda3.push(a3); preda4.push(a4);       
                    return [tmax,tmin,newicon,rain,snow,wsmax,wsmin,boemax,a1,a2,a3,a4];
                }

                                                            
                if(arraycount[0]==utcdayd && utchourd<=22) { var tag0 = dia0();  }   
                
                console.log("propOwn.length: "+propOwn);
                
                for(var m=1;m<propOwn.length;m++) { 
                                        if(arraycount2[m] >= 4) { var zb2=zb+arraycount2[m]; 
                                                                 
                                                                 var hora = neueuhrzeiten[zb].split(', '); var parts = hora[0].split('.'); 
                                                                 var horaeinuhr = parts[2]+"-"+zweistellig(parts[1])+"-"+zweistellig(parts[0])+"T"+zweistellig(hora[1])+":00Z";   
                                                                 var dt = new Date(horaeinuhr); 
                                                                 var datumrechnen = dt.toLocaleString('en-EN', {timeZone: "Europe/Madrid", weekday:"short", day: "numeric", month: "short"}); 
                                                                 var temp = []; var rain = parseFloat(0); var snow = 0; var cielo = []; var presion = []; var wspeed = []; var boens = []; var humi = [];  var array06=[]; var array612=[]; var array1218=[]; var array1824=[];
                                                                 for(var i=zb;i<zb2;i++) {
                                                                     var tgm = parseFloat(temperatur[i]).toFixed(0); if (tgm=="-0") { temp.push("0"); } else { temp.push(tgm); }
                                                                     if (icons[i]!="empty") { var numb = icons[i].match(/\d/g); numb = numb.join(""); cielo.push(numb); }  
                                                                     var lluvia = parseFloat(regen[i]).toFixed(2); if(lluvia>="0.02") { rain = parseFloat(rain) + parseFloat(lluvia); } 
                                                                     var nieve = parseFloat(schnee[i]).toFixed(2); if(nieve>0) { snow =  parseFloat(snow) + parseFloat(nieve); }
                                                                     
                                                                     var speed = parseFloat(ws[i]); wspeed.push(speed); 
                                                                     var boe = parseFloat(boen[i]); boens.push(boe);                                                 
                                                                       
                                                                     var slice = neueuhrzeiten[i].slice(12, 14);
                                                                     fcielo(slice,numb);
                                                                 }
                                                                        var upside = temp.sort(function(a, b){return a-b});  var himmel = cielo.sort(function(a, b){return a-b});
                                                                        var zahl = upside.length-1; var zahlcie = himmel.length-1; var tmax = upside[zahl]; var tmin = upside[0]; var heaven = himmel[zahlcie];
                                                                        var uniqueList = Array.from(new Set(cielo));  var lang = uniqueList.length-1;   var newicon =heaven;           
                                                                        if(uniqueList[lang]>=80&&uniqueList[lang]<=86) { if(uniqueList[lang-1]>=61) { var newicon = uniqueList[lang-1]; } else { var newicon = uniqueList[lang]; }  }
                                                                        
                                                                        var sortwind = wspeed.sort(function(a, b){return a-b}); var wsmax = windgeschwindigkeit(sortwind[zahl]); var wsmin = windgeschwindigkeit(sortwind[0]);
                                                                        var sortboe = boens.sort(function(a, b){return a-b}); var boemax = windgeschwindigkeit(sortboe[zahl]); 
                                                                        
                   predtage.push(datumrechnen); predtmax.push(tmax); predtmin.push(tmin); predicon.push(newicon); predrain.push(rain); predsnow.push(snow); predwsmax.push(wsmax); predwsmin.push(wsmin); predboemax.push(boemax); 
                        var a1 = mode(array06);  var a2 = mode(array612); var a3 = mode(array1218); var a4 = mode(array1824);  preda1.push(a1); preda2.push(a2); preda3.push(a3); preda4.push(a4);                                           
                                                                }
                                        zb=zb+arraycount2[m];
                }
                

                var inhalt=""; 
                
                
                for(var i=0;i<predtage.length;i++) { var tabpred=""; var tabwin="";
                var div="preddia"+i;                    
                    if(predrain[i]>0) { tabpred='<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-droplet-fill" viewBox="0 0 16 16"><path d="M8 16a6 6 0 0 0 6-6c0-1.655-1.122-2.904-2.432-4.362C10.254 4.176 8.75 2.503 8 0c0 0-6 5.686-6 10a6 6 0 0 0 6 6M6.646 4.646l.708.708c-.29.29-1.128 1.311-1.907 2.87l-.894-.448c.82-1.641 1.717-2.753 2.093-3.13"/></svg> ' + parseFloat(regenausgabe(predrain[i])).toFixed(2); }  
                    if(predsnow[i]>0) { tabpred += '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-snow2" viewBox="0 0 16 16"><path d="M8 16a.5.5 0 0 1-.5-.5v-1.293l-.646.647a.5.5 0 0 1-.707-.708L7.5 12.793v-1.086l-.646.647a.5.5 0 0 1-.707-.708L7.5 10.293V8.866l-1.236.713-.495 1.85a.5.5 0 1 1-.966-.26l.237-.882-.94.542-.496 1.85a.5.5 0 1 1-.966-.26l.237-.882-1.12.646a.5.5 0 0 1-.5-.866l1.12-.646-.884-.237a.5.5 0 1 1 .26-.966l1.848.495.94-.542-.882-.237a.5.5 0 1 1 .258-.966l1.85.495L7 8l-1.236-.713-1.849.495a.5.5 0 1 1-.258-.966l.883-.237-.94-.542-1.85.495a.5.5 0 0 1-.258-.966l.883-.237-1.12-.646a.5.5 0 1 1 .5-.866l1.12.646-.237-.883a.5.5 0 0 1 .966-.258l.495 1.849.94.542-.236-.883a.5.5 0 0 1 .966-.258l.495 1.849 1.236.713V5.707L6.147 4.354a.5.5 0 1 1 .707-.708l.646.647V3.207L6.147 1.854a.5.5 0 1 1 .707-.708l.646.647V.5a.5.5 0 0 1 1 0v1.293l.647-.647a.5.5 0 1 1 .707.708L8.5 3.207v1.086l.647-.647a.5.5 0 1 1 .707.708L8.5 5.707v1.427l1.236-.713.495-1.85a.5.5 0 1 1 .966.26l-.236.882.94-.542.495-1.85a.5.5 0 1 1 .966.26l-.236.882 1.12-.646a.5.5 0 0 1 .5.866l-1.12.646.883.237a.5.5 0 1 1-.26.966l-1.848-.495-.94.542.883.237a.5.5 0 1 1-.26.966l-1.848-.495L9 8l1.236.713 1.849-.495a.5.5 0 0 1 .259.966l-.883.237.94.542 1.849-.495a.5.5 0 0 1 .259.966l-.883.237 1.12.646a.5.5 0 0 1-.5.866l-1.12-.646.236.883a.5.5 0 1 1-.966.258l-.495-1.849-.94-.542.236.883a.5.5 0 0 1-.966.258L9.736 9.58 8.5 8.866v1.427l1.354 1.353a.5.5 0 0 1-.707.708l-.647-.647v1.086l1.354 1.353a.5.5 0 0 1-.707.708l-.647-.647V15.5a.5.5 0 0 1-.5.5"/></svg ' + parseFloat(predsnow[i]).toFixed(1); }
                                       
                    if (predboemax[i]>20) { tabwin ='<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-wind" viewBox="0 0 16 16"><path d="M12.5 2A2.5 2.5 0 0 0 10 4.5a.5.5 0 0 1-1 0A3.5 3.5 0 1 1 12.5 8H.5a.5.5 0 0 1 0-1h12a2.5 2.5 0 0 0 0-5m-7 1a1 1 0 0 0-1 1 .5.5 0 0 1-1 0 2 2 0 1 1 2 2h-5a.5.5 0 0 1 0-1h5a1 1 0 0 0 0-2M0 9.5A.5.5 0 0 1 .5 9h10.042a3 3 0 1 1-3 3 .5.5 0 0 1 1 0 2 2 0 1 0 2-2H.5a.5.5 0 0 1-.5-.5"/></svg>'; }                 
                    
                    
                    inhalt +="<div class='watwetter' id='"+div+"'>";
                    inhalt +="<table class='wetterprediccion'><thead><tr><td colspan='2'>"+predtage[i]+"</td></tr></thead><tbody><tr><td class='regen'>"+tabpred+"</td><td class='maxtemp'>"+tempausgabe(predtmax[i])+"°</td></tr><tr><td class='winddia'>"+tabwin+"</td><td class='mintemp'>"+tempausgabe(predtmin[i])+"°</td></tr></tbody></table>"
                    inhalt +="</div>"
                    
                
                document.getElementById("predicciontage2").innerHTML=inhalt;
                
                
                }
                
                
                                
                
            }
        }
    }
    rawFile.send(null);
    
var div0 = document.getElementById("item0").offsetWidth+40; 
var div1 = document.getElementById("item1").offsetWidth+50; 
var div2 = document.getElementById("item2").offsetWidth+50; 
var div3 = document.getElementById("item3").offsetWidth+50; 
var div4 = document.getElementById("item4").offsetWidth+50; 

 var zer1=div0+div1;    
 var zer2=div0+div1+div2;    
 var zer3=div0+div1+div2+div3;    
 var zer4=div0+div1+div2+div3+div4;    
     
        
        
  $('#preddia0').click(function(){  $('.menuwetter').animate( { scrollLeft: '0' }, 300); });        
  $('#preddia1').click(function(){ var le = div0; $('.menuwetter').animate( { scrollLeft: le }, 300); });        
  $('#preddia2').click(function(){ var le = div0+div1; $('.menuwetter').animate( { scrollLeft: le }, 300); });        
  $('#preddia3').click(function(){ var le = div0+div1+div2; $('.menuwetter').animate( { scrollLeft: le }, 300); });        
  $('#preddia4').click(function(){ var le = div0+div1+div2+div3; $('.menuwetter').animate( { scrollLeft: le }, 300); });  
    var element = document.getElementById('preddia5');
 if (typeof (element) != undefined && typeof (element) != null && typeof (element) != 'undefined') {
   $('#preddia5').click(function(){ var le = div0+div1+div2+div3+div4; $('.menuwetter').animate( { scrollLeft: le }, 300); });  
 }  
    
}
        
var element = document.getElementById("preddia0"); element.classList.add("activea");        
        
var div0 = document.getElementById("item0").offsetWidth+40; 
var div1 = document.getElementById("item1").offsetWidth+50; 
var div2 = document.getElementById("item2").offsetWidth+50; 
var div3 = document.getElementById("item3").offsetWidth+50; 
var div4 = document.getElementById("item4").offsetWidth+50; 

 var zer1=div0+div1;    
 var zer2=div0+div1+div2;    
 var zer3=div0+div1+div2+div3;    
 var zer4=div0+div1+div2+div3+div4;    
     
        
        
        
        
     
    function checkscroll(menuPosition) { var menuPosition;
        
if (menuPosition < div0) {
     document.getElementById("preddia0").classList.add("activea"); var stuff=["preddia1","preddia2","preddia3","preddia4"]; for (var i=0;i<stuff.length;i++) { document.getElementById(stuff[i]).classList.remove("activea"); }
}
else if ( menuPosition >= div0 &&  menuPosition < zer1){
     document.getElementById("preddia1").classList.add("activea"); var stuff=["preddia0","preddia2","preddia3","preddia4"]; for (var i=0;i<stuff.length;i++) { document.getElementById(stuff[i]).classList.remove("activea"); }
}
else if ( menuPosition >= zer1 &&  menuPosition < zer2) {
     document.getElementById("preddia2").classList.add("activea"); var stuff=["preddia1","preddia0","preddia3","preddia4"]; for (var i=0;i<stuff.length;i++) { document.getElementById(stuff[i]).classList.remove("activea"); }
}
else if ( menuPosition >= zer2 &&  menuPosition < zer3) {
     document.getElementById("preddia3").classList.add("activea"); var stuff=["preddia1","preddia2","preddia0","preddia4"]; for (var i=0;i<stuff.length;i++) { document.getElementById(stuff[i]).classList.remove("activea"); }
}
else if ( menuPosition >= zer3) {
     document.getElementById("preddia4").classList.add("activea"); var stuff=["preddia1","preddia2","preddia3","preddia0"]; for (var i=0;i<stuff.length;i++) { document.getElementById(stuff[i]).classList.remove("activea"); }
}
        
        
    }    
        
        
        
        
     function readTextFile22(datenvorher)
{ console.log("datenvorher: "+datenvorher);
    var rawFile = new XMLHttpRequest();
 var wellenurl="malaga";
    rawFile.open("GET", "puertos/"+wellenurl+".json", false);
    rawFile.onreadystatechange = function ()
    {
        if(rawFile.readyState === 4)
        {
            if(rawFile.status === 200 || rawFile.status == 0)
            {
                var allText = rawFile.responseText;
                var myArray = allText.split(/\r?\n/); 
                var zeit = myArray[0]; 
                var myArrayd = zeit.split("["); var hierarrayse = myArrayd[1].replace(/'/g, ''); var uhrzeit = hierarrayse.split(", "); 
                
                
                var swh = myArray[1].split(","); 
                var mwp = myArray[2].split(",");
                var mwd = myArray[3].split(",");
                
                                
                
                var now = new Date; const utch = now.getUTCHours(); const utcday = now.getUTCDate(); const utcmo = now.getUTCMonth()+1; const utcyear = now.getUTCFullYear();
                const neuesdatum = parseInt(utcday)+"."+utcmo+"."+utcyear+" "+utch;

               function zweistellig (s) { while (s.toString().length < 2) {s = "0" + s;} return s; }	
               function einstellig(s) { const firstDigitStr = String(s)[0]; const second = String(s)[1]; if(firstDigitStr==0) { s = second; } return s; }
                
                const greaterThan = uhrzeit.indexOf(neuesdatum);
                const greaterThanTen = greaterThan;
                
                let minutes = (parseFloat(timeoffset) / (1 * 60)).toFixed(0);
                
                let result = minutes.includes("-");
                var end12horas = uhrzeit.length;
                
                
                
                const datumfueruhrzeit = new Date(); var uhrzeitvorort2 = datumfueruhrzeit.toLocaleString('de-DE', {timeZone: "Europe/Madrid", hour:"2-digit"}); var uhrzeitvorort = uhrzeitvorort2.replace(" Uhr", "");
                const datumr = new Date(); var datumrechnen = datumr.toLocaleString('de-DE', {timeZone: "Europe/Madrid", day:"2-digit", month:"2-digit", year:"numeric"}); 
                var datumrechnenutc = datumr.toLocaleString('de-DE', { timeZone: "UTC", day:"2-digit", month:"2-digit", year:"numeric"});
                var whichday; 
                if (datumrechnen == datumrechnenutc) { whichday = "0";} else if (datumrechnen > datumrechnenutc) { whichday = "1";} else if (datumrechnen < datumrechnenutc) { whichday = "2";}
                

                var waveeinheit = document.getElementById('wellenhoehe').value;
                function waveausgabe(result) { if (waveeinheit=="ft") { var menge = result*3.28084;} else { var menge = result; }  return parseFloat(menge).toFixed(1); }  
                
                
                var tagnamen=[]; var anzahl=[];
for (var i=greaterThanTen;i<end12horas;i++) { var parts = uhrzeit[i].split('.'); var hora = parts[2].split(' '); 
                    var horaeinuhr = hora[0]+"-"+zweistellig(parts[1])+"-"+zweistellig(parts[0])+"T"+zweistellig(hora[1])+":00:00Z";    
                    var dt = new Date(horaeinuhr);   dt.setMinutes(dt.getMinutes()); 
                     tagnamen.push(dt.toLocaleString('de-DE', {weekday:"long"})); }
                
                const countsT = {};
                tagnamen.forEach(function (x) { countsT[x] = (countsT[x] || 0) + 1; });
                const propOwnT = Object.getOwnPropertyNames(countsT);
                
                var tabauf="";
                for(key in countsT) {
                    if(countsT.hasOwnProperty(key)) {
                        var value = countsT[key];
                        anzahl.push(value);
                    }
                }
                
                var zahl1=greaterThanTen; var zahl2; var tablewetterdia0;
                
               for(var q=0;q<anzahl.length-1;q++) { 
               
                   zahl2=zahl1+anzahl[q];
                   
                   
                   tablewetterdia0="";
                   
                   tablewetterdia0="<table class='wetter'><tr>";
                   
                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+datenvorher[q]+"'></td></tr><tr>";
                    
                   for (var i=zahl1;i<zahl2;i++) { tablewetterdia0 += "<td>" + waveausgabe(swh[i]) +"</td>"; } if(datenvorher[q]!=anzahl[q]) { var nochdazu=datenvorher[q]-anzahl[q]; tablewetterdia0 += "<td colspan='"+nochdazu+"'></td>";  } 

                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+datenvorher[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { tablewetterdia0 += "<td class='wind'><div style='transform: rotate("+mwd[i]+"deg);'>&darr;</div><div style='display:inline; font-size:11px;'>" + degToCompassshort(mwd[i]) +" </div></td>"; }  if(datenvorher[q]!=anzahl[q]) { var nochdazu=datenvorher[q]-anzahl[q]; tablewetterdia0 += "<td colspan='"+nochdazu+"'></td>";  } 
 
                   tablewetterdia0+="</tr><tr class='leerestr'><td colspan='"+datenvorher[q]+"'></td></tr><tr>";

                   for (var i=zahl1;i<zahl2;i++) { tablewetterdia0 += "<td>" + parseFloat(mwp[i]).toFixed(0) +"</td>"; }  if(datenvorher[q]!=anzahl[q]) { var nochdazu=datenvorher[q]-anzahl[q]; tablewetterdia0 += "<td colspan='"+nochdazu+"'></td>";  } 

                   tablewetterdia0+="</tr></table>";   
                   
                    var div="item"+q;

                   document.getElementById(div).innerHTML+=tablewetterdia0;
                   
                    zahl1=zahl1+anzahl[q];
               

                
                }
                


                
                
            }
        }
    }
    rawFile.send(null);
}
   
     
   

        
        
        
        
        
        
        
        
        
        
      function mode(array)
{
    if(array.length == 0)
        return null;
    var modeMap = {};
    var maxEl = array[0], maxCount = 1;
    for(var i = 0; i < array.length; i++)
    {
        var el = array[i];
        if(modeMap[el] == null)
            modeMap[el] = 1;
        else
            modeMap[el]++;  
        if(modeMap[el] > maxCount)
        {
            maxEl = el;
            maxCount = modeMap[el];
        }
    }
    return maxEl;
}
      
        
  $('#wettertab').hover(function() {
document.getElementsByClassName("paddle")[0].style.backgroundColor="rgba(0,0,0,0.6)";
document.getElementsByClassName("paddle")[1].style.backgroundColor="rgba(0,0,0,0.6)";
  }, function() {
document.getElementsByClassName("paddle")[0].style.backgroundColor="rgba(0,0,0,0.2)";
document.getElementsByClassName("paddle")[1].style.backgroundColor="rgba(0,0,0,0.2)";
});      
   
        
      
    
var Charttemp, Chartwind, Chartwind2;
      
var urlfirst = "https://cam.meteo365.es/station/malaga.txt"; var timez = "7200";
        
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////// FUNCTION STATION BEGINN  
    
function station() {  
    
var windeinstellung = document.getElementById('laenge').value; var einheit;
var tempeinheit = document.getElementById("gradselect").value;	var gradeinheit; 
    
function windgesch(result) {
     if(windeinstellung=="kn") { var menge = result/1.852; } 
else if (windeinstellung=="ms") { var menge = result/3.6; }								
else if (windeinstellung=="mph") { var menge = result*0.621; }								
else if (windeinstellung=="fts") { var menge = result*0.91; }								
else {	var menge = result;}

return parseFloat(menge).toFixed(0);	
} 
var valuerain = document.getElementById('regenmenge').value;  
function regenausgabe(result) { 
if (result==0) { return 0; } else { if (valuerain=="inch") { var menge = result/25.4;} else { var menge = result; } return parseFloat(menge).toFixed(2); }
 } 
    
if(speed=="kn") { einheit = "kn"; } else if (speed=="ms") { einheit = "m/s";  } else if (speed=="mph") { einheit = "mph";  } else if (speed=="fts") { einheit = "ft/s";  } else { einheit = "km/h"; }  
if(tempeinheit=="fahrenheit") { gradeinheit = "°F"; } else { gradeinheit = "°C"; }      
                    
function tempausgabe(result) { if(tempeinheit=="fahrenheit") { var menge = result *(9/5) +32 } else { var menge = result; }  return parseFloat(menge).toFixed(1); }        
                         
for (var k =0;k<3;k++) { document.getElementsByClassName("einheitboe")[k].innerHTML= einheit; }                 
for (var k =0;k<2;k++) { document.getElementsByClassName("einheittemp")[k].innerHTML= gradeinheit; }
for (var k =0;k<2;k++) { document.getElementsByClassName("einheitrain")[k].innerHTML= valuerain; }                
        
$.ajax({
  url: urlfirst,
  success: function(datan) {
    
 var guadal = "malaga";      
if(guadal=="nerja"||guadal=="benclub"||guadal=="benalmadena2"||guadal=="marbella") { var lines = datan.split(";"); } else { var lines = datan.split(","); }       

var temp =   lines[0].split(':');  if(temp[3].indexOf(",") !== -1) { temp[3] = temp[3].replace(",","."); console.log("temp[3]: if"); } else { console.log("temp[3]: else"); } console.log("temp[3]: "+temp[3]);
var result =   lines[1].split(':');
var windg =   lines[2].split(' ');  
var rachas =   lines[3].split(' ');
var hum =   lines[4].split(':');  var humedad =  hum[1].split('%'); 
var llu =   lines[5].split(' ');
var pres =   lines[6].split(' ');
var bild = result[1].replace(" ","");
      var wind_dir_degrees = lines[8].split(' '); 
      var rainday = llu[3]; 
      var rainday = llu[3]; var ttzzzzed = temp[3].replace(' ', ''); ttzzzzed = ttzzzzed.replace(' ', ''); 
      if(ttzzzzed=="60.0") { document.getElementById("emtemp").innerHTML = "nd"; console.log("nd:"+ttzzzzed); } else { document.getElementById("emtemp").innerHTML = tempausgabe(temp[3]); console.log("temp[3]:"+ttzzzzed); }
     document.getElementById("emwind").innerHTML = windgesch(windg[1]);
     document.getElementById("emgusts").innerHTML = windgesch(rachas[1]);
     document.getElementById("emhum").innerHTML = humedad[0];
     document.getElementById("emrain").innerHTML = regenausgabe(rainday);
     document.getElementById("empres").innerHTML = pres[2];
      
      var scala = beaufort(windg[1], {lang: 'en', int: true});
      var color = scala["color"]; var stark = scala["grade"];
      
document.getElementById("emwindbild").innerHTML = "<span class='bfscalac' style='background-color:rgb("+color+");'>"+stark+"</span>";
      
var x = wind_dir_degrees[2];      
document.getElementById("winddirection").style.transform = "rotate("+x+"deg)"; 
function degToCompass(num) {
    var val = Math.floor((num / 22.5) + 0.5);
    var arr = ["North", "North-Northeast", "Northeast", "East-Northeast", "East", "East Southeast", "Southeast", "South Southeast", "South", "South Southwest", "Southwest", "West Southwest", "West", "West Northwest", "Northwest", "North Northwest"];
    return arr[(val % 16)];
}
document.getElementById("winddirection2").innerHTML = "<div class='winddirectionsmall'>WIND DIRECTION: </div>"+degToCompass(x);   
           
document.getElementById("bfscala").innerHTML = scala["desc"];  var bpo=lines[9].replaceAll("-","/");   var dat = bpo.replace("T"," "); dat = dat.replace("Z"," "); 
     
      const d = new Date(dat); d.setHours(d.getHours() + 2); let text = d.toLocaleString('en-EN', {day:"2-digit", month:"2-digit", hour:"2-digit", minute:"2-digit"}); console.log("d",d+"dat",dat);
      
    var timez = "7200";
var horaemtime = temp[1].split(' ');       
if (timez=="7200") { var hora = parseFloat(temp[0])+2; } else { var hora = parseFloat(temp[0])+1; }
if(hora=="24") { var timestunde = "00"; } else if (hora=="25") { var timestunde = "01"; } else { var timestunde = hora; }        if(guadal=="aeropuerto"||guadal=="lagos"||guadal=="sotogrande") { document.getElementById("emzeit").innerHTML = timestunde+":"+horaemtime[0]; } else { document.getElementById("emzeit").innerHTML = text;  } 
       }}) //timestunde+":"+horaemtime[0];


var messzeit = []; var temp = []; var regen = []; var regen2 = []; var wind = []; var rachas = []; 
var urllast = "https://cam.meteo365.es/station/malaga/lasthours.txt";
                                                                             
$.ajax({
  url: urllast,
  success: function(datan) {
      var guadal = "malaga"; 
var lines = datan.split('\n');
    for(var line = 1; line < lines.length-1; line++){
        if(guadal=="nerja1"||guadal=="benclub"||guadal=="benalmadena2") { var uhrzeit = lines[line].split(";"); } else { var uhrzeit = lines[line].split(","); }  
        var plush = "2";
        var hora = uhrzeit[0].split(":");
        
        if(plush=="2") { if (hora[1]=="00 UTC") { var horaend2 = parseFloat(hora[0])+2;} else { var horaend2 = parseFloat(hora[0])+3;}  } else { if (hora[1]=="00 UTC") { var horaend2 = parseFloat(hora[0])+1;} else { var horaend2 = parseFloat(hora[0])+2;} } 
        
        var horaendtime = horaend2; var horaend; if(horaendtime=="24") { horaend = "00 h"; } else if (horaendtime=="25") { horaend = "1 h"; } else if (horaendtime=="26") { horaend = "2 h"; } else if (horaendtime=="27") { horaend = "3 h"; }  else { horaend = horaendtime + " h"; } if (uhrzeit[2]=="0.0"||uhrzeit[2]=="0,0") { var rain = 0; } else { var rain = uhrzeit[2]; }   var tte = uhrzeit[1].replace(",",".");  if(tte=="60.0") { temp.push("nd"); } else { temp.push(tempausgabe(tte)); } messzeit.push(horaend); regen.push(rain); regen2.push(uhrzeit[2]); wind.push(windgesch(uhrzeit[3])); rachas.push(windgesch(uhrzeit[4]));
    }
     
var regentable = "<table class='regen'><tr><td><span class='tear'>"+regenausgabe(regen[0])+"</span></td><td><span class='tear'>"+regenausgabe(regen[1])+"</span></td><td><span class='tear'>"+regenausgabe(regen[2])+"</span></td><td><span class='tear'>"+regenausgabe(regen[3])+"</span></td><td><span class='tear'>"+regenausgabe(regen[4])+"</span></td><td><span class='tear'>"+regenausgabe(regen[5])+"</span></td></tr></table>";

document.getElementById("rainchart").innerHTML = regentable;
        
Chart.register(ChartDataLabels);

var options = {
  data: {
    labels: messzeit,
      datasets: [{
      type: 'line',      
      label: 'Temp',
      data: temp,
      borderColor: '#b81f6f', backgroundColor: '#b81f6f',
      borderWidth: 3
    }]
  },
  options: { responsive: true, maintainAspectRatio: false,
      layout: {
            padding: 10
        },
      scales: {
        yAxis: { display: false },
        xAxis: { position: 'top', ticks: { color: '#0e1e2e' },grid: {display: false} } },  responsive: true, maintainAspectRatio: false,
      elements: {
        line: {
            tension: 0.4 
        }
    },
    plugins: { legend: { display: false},
      datalabels: {
        backgroundColor: function(context) {
          return context.dataset.backgroundColor;
        },
        borderColor: 'white',
        borderRadius: 20,
        borderWidth: 0,
        color: 'white',
        font: {
          weight: 'bold'
        },
        padding: 4,
      }
    }
  }
}

var ctx = document.getElementById('last6hours').getContext('2d');
Charttemp = new Chart(ctx, options);    
  
var options2 = {
  data: {
    labels: messzeit,
      datasets: [{
      type: 'line',      
      label: 'Wind',
      data: wind,
      borderColor: '#3fd99b', backgroundColor: '#3fd99b',
      borderWidth: 3
    }]
  },
  options: { responsive: true, maintainAspectRatio: false,
      layout: {
            padding: 10
        },
      scales: {
        yAxis: { display: false },
        xAxis: { display: false } },  responsive: true, maintainAspectRatio: false,
      elements: {
        line: {
            tension: 0.4 
        }
    },
    plugins: { legend: { display: false},
      datalabels: {
        backgroundColor: function(context) {
          return context.dataset.backgroundColor;
        },
        borderColor: 'white',
        borderRadius: 50,
        borderWidth: 0,
        color: 'white',
        font: {
          weight: 'bold'
        },
        padding: 6,
          title: {
            color: 'blue'
          }
      }
    }
  }
}

var ctx2 = document.getElementById('last6hourswind').getContext('2d');
Chartwind = new Chart(ctx2, options2);    

      
var options3 = {
  data: {
    labels: messzeit,
      datasets: [{
      type: 'line',      
      label: 'Rachas',
      data: rachas,
      borderColor: '#0a705f', backgroundColor: '#0a705f',
      borderWidth: 3
    }]
  },
  options: { responsive: true, maintainAspectRatio: false,
      layout: {
            padding: 10
        },
      scales: {
        yAxis: { display: false },
        xAxis: { display: false },  
    },
    plugins: { legend: { display: false},
      datalabels: {
        backgroundColor: function(context) {
          return context.dataset.backgroundColor;
        },
        borderColor: 'white',
        borderRadius: 50,
        borderWidth: 0,
        color: 'white',
        font: {
          weight: 'bold'
        },
        padding: 6,
      }
    }
  }
}

var ctx3 = document.getElementById('last6hourswind2').getContext('2d');
Chartwind2 = new Chart(ctx3, options3);    

      
      
      
  }
});
      
 }    

   station();
     </script>
     <script async="" crossorigin="anonymous" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7399755642929424">
     </script>
     <span id="werbung4">
     </span>
     <div style="clear: both;">
     </div>
     <br/>
     <br/>
     <div id="contentindex">
      <div class="camsortindex4">
       Live - webcams in the Province of Malaga &amp; Cádiz - Costa del Sol
      </div>
      <div class="cams1">
       <div class="camstart">
        <a href="malaga-farola.php">
         <div class="camstarttext">
          Malaga Port - lighthouse
         </div>
         <img alt="Webcam Malaga Port" class="imagestart" src="thumbs/cams/malaga-puerto-cam1_neu.webp" title="Webcam Malaga Port">
          <div class="live">
          </div>
         </img>
        </a>
       </div>
       <div class="camstart">
        <a href="puerto-malaga.php">
         <div class="camstarttext">
          Malaga Port
         </div>
         <img alt="Webcam Malaga Port" class="imagestart" src="thumbs/cams/malaga-puerto-cam2_neu.webp" title="Webcam Malaga Port"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="malaga-muelle.php">
         <div class="camstarttext">
          Malaga Port - Muelle 1 &amp; 2
         </div>
         <img alt="Webcam Malaga Port" class="imagestart" src="thumbs/cams/malaga-puerto-cam3_neu.webp" title="Webcam Malaga Port"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="malaga.php">
         <div class="camstarttext">
          Malaga - La Malagueta Beach
         </div>
         <img alt="Webcam Malaga, Beach La Malagueta" class="imagestart" src="thumbs/cams/malagaplaya.jpg" title="Webcam Malaga, Beach La Malagueta"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="malaga-puerto.php">
         <div class="camstarttext">
          Port of Malaga
         </div>
         <img alt="Webcam Malaga Hafen" class="imagestart" src="thumbs/cams/malagapuerto.jpg" title="Webcam Malaga Hafen"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--</div> 
<div class="cams1">  -->
       <div class="camstart">
        <a href="malaga-misericordia.php">
         <div class="camstarttext">
          Malaga - La Misericordia Beach
         </div>
         <img alt="Webcam Malaga Beach La Misericordia" class="imagestart" src="thumbs/cams/misericordia.jpg" title="Webcam Malaga Beach La Misericordia"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="malaga-alcazaba.php">
         <div class="camstarttext">
          La Alcazaba in Malaga
         </div>
         <img alt="Webcam Alcazaba in Malaga" class="imagestart" src="thumbs/cams/alcazaba.jpg" title="Webcam Alcazaba in Malaga"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="malaga-alameda.php">
         <div class="camstarttext">
          Alameda Principal, Málaga
         </div>
         <img alt="Webcam Alameda Principal de Malaga" class="imagestart" src="thumbs/cams/malagalameda.jpg" title="Webcam Alameda Principal de Malaga"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--<div class="camstart"><a href="malaga-plaza-constitucion.php"> <div class="camstarttext">Málaga - Plaza de la Constitición</div> <img src="thumbs/cams/malagaplaza.jpg" class="imagestart" alt="Webcam Malaga Plaza de la Constitución" title="Webcam Malaga Plaza de la Constitución" /> <div class="live"></div></a></div>   -->
       <div class="camstart">
        <a href="malaga-airport.php">
         <div class="camstarttext">
          Airport Malaga - Costa del Sol
         </div>
         <img alt="Webcam Airport Malaga - Costa del Sol" class="imagestart" src="thumbs/cams/malagaero.jpg" title="Webcam Airport Malaga - Costa del Sol"/>
         <div class="live">
         </div>
        </a>
       </div>
      </div>
      <div class="cams1">
       <div class="camstart">
        <a href="torremolinos-bajondillo.php">
         <div class="camstarttext">
          Torremolinos - Bajondillo Beach
         </div>
         <img alt="Webcam Torremolinos - Bajondillo Beach" class="imagestart" src="thumbs/cams/torremolinosplaya.jpg" title="Webcam Torremolinos - Bajondillo Strand"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="torremolinos.php">
         <div class="camstarttext">
          Torremolinos - Promenade
         </div>
         <img alt="Webcam Torremolinos - promenade" class="imagestart" src="thumbs/cams/torremolinosprom.jpg" title="Webcam Torremolinos - promenade"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <!--</div> 
<div class="cams1">  -->
       <div class="camstart">
        <a href="benalmadena-bilbil.php">
         <div class="camstarttext">
          Benalmadena - Bil Bil Castle
         </div>
         <img alt="Webcam Benalmadena - Bil Bil Castle" class="imagestart" src="thumbs/cams/benalmadenabilbil.jpg" title="Webcam Benalmadena - Bil Bil Castle"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="benalmadena.php">
         <div class="camstarttext">
          Benalmadena - Bil-Bil Beach
         </div>
         <img alt="Webcam Benalmadena - Bil-Bil Beach" class="imagestart" src="thumbs/cams/benalmadenaplaya.jpg" title="Webcam Benalmadena - Bil-Bil Beach"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="benalmadena-club.php">
         <div class="camstarttext">
          Benalmadena - Puerto Marina
         </div>
         <img alt="Webcam Benalmadena - Puerto Marina" class="imagestart" src="thumbs/cams/benalmadenaclub.jpg" title="Webcam Benalmadena - Puerto Marina"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--<div class="camstart"><a href="benalmadena-puerto.php"> <div class="camstarttext">Benalmadena - Puerto Marina II</div> <img src="thumbs/cams/benalmadenaclub2.jpg" class="imagestart" alt="Webcam Benalmadena - Puerto Marina" title="Webcam Benalmadena - Puerto Marina" /> <div class="live"></div></a></div>  -->
       <div class="camstart">
        <a href="benalmadena-puerto-bocana.php">
         <div class="camstarttext">
          Benalmadena - Puerto Deportivo
         </div>
         <img alt="Webcam Benalmadena - Puerto Deportivo Bocana" class="imagestart" src="thumbs/cams/benalmadenabocana.jpg" title="Webcam Benalmadena - Puerto Deportivo Bocana"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="benalmadena-puerto-marina.php">
         <div class="camstarttext">
          Benalmádena - Puerto Deportivo II
         </div>
         <img alt="Webcam Benalmadena - Puerto Deportivo Bocana" class="imagestart" src="thumbs/cams/benalmadena-marina.jpg" title="Webcam Benalmadena - Puerto Deportivo Marina"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--</div> 
<div class="cams1">  -->
       <div class="camstart">
        <a href="mijas.php">
         <div class="camstarttext">
          Mijas - Las Doradas Beach
         </div>
         <img alt="Webcam Mijas - Playa Las Doradas" class="imagestart" src="thumbs/cams/mijassheriff.jpg" title="Webcam Mijas - Playa Las Doradas"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="alhaurindelatorre.php">
         <div class="camstarttext">
          Alhaurín de la Torre
         </div>
         <img alt="Webcam Alhaurin de la Torre" class="imagestart" src="thumbs/cams/cam-alhaurindelatorre.webp" title="Webcam Alhaurin de la Torre"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="torredelmar.php">
         <div class="camstarttext">
          Lagos - View to Torre del Mar
         </div>
         <img alt="Webcam Lagos - View to Torre del Mar" class="imagestart" src="thumbs/cams/lagos.jpg" title="Webcam Lagos - View to Torre del Mar"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--<div class="camstart"><a href="lagos-costa.php"> <div class="camstarttext">Lagos - View to Torrox Costa</div> <img src="thumbs/cams/torrox.jpg" class="imagestart" alt="Webcam Lagos - View to Torrox Costa" title="Webcam Lagos - View to Torrox Costa" /> <div class="livebild"></div></a></div> -->
       <!--</div> 
<div class="cams1">  -->
       <div class="camstart">
        <a href="nerja-balcon-europa.php">
         <div class="camstarttext">
          Nerja - Balcón de Europa
         </div>
         <img alt="Webcam Nerja - Balcón de Europa" class="imagestart" src="thumbs/cams/nerja.jpg" title="Webcam Nerja - Balcón de Europa"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="nerja-playa.php">
         <div class="camstarttext">
          Nerja - El Salón Beach
         </div>
         <img alt="Webcam Nerja - Playa El Salón" class="imagestart" src="thumbs/cams/nerjaplaya.jpg" title="Webcam Nerja - Playa El Salón"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="fuengirola.php">
         <div class="camstarttext">
          Fuengirola - Las Gaviotas Beach
         </div>
         <img alt="Webcam Fuengirola - Las Gaviotas Beach" class="imagestart" src="thumbs/cams/fglplaya.jpg" title="Webcam Fuengirola - Las Gaviotas Beach"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="fuengirola-puerto.php">
         <div class="camstarttext">
          Fuengirola - Port &amp; Los Boliches
         </div>
         <img alt="Webcam Fuengirola - Port &amp; Los Boliches" class="imagestart" src="thumbs/cams/fglpuerto.jpg" title="Webcam Fuengirola - Port &amp; Los Boliches"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="marbella.php">
         <div class="camstarttext">
          Marbella - Playa del Cable
         </div>
         <img alt="Webcam Marbella" class="imagestart" src="thumbs/cams/marbella.jpg" title="Webcam Marbella"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="sotogrande.php">
         <div class="camstarttext">
          Puerto Sotogrande I
         </div>
         <img alt="Webcam Puerto Sotogrande" class="imagestart" src="thumbs/cams/puertosotogrande2.jpg" title="Webcam Puerto Sotogrande"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="puerto-sotogrande.php">
         <div class="camstarttext">
          Puerto Sotogrande II
         </div>
         <img alt="Webcam Puerto Sotogrande" class="imagestart" src="thumbs/cams/puertosotogrande1.jpg" title="Webcam Puerto Sotogrande"/>
         <div class="live">
         </div>
        </a>
       </div>
      </div>
      <div class="camsortindex4">
       Live - webcams in Andalusia
      </div>
      <div class="cams1">
       <div class="camstart">
        <a href="malaga-ronda.php">
         <div class="camstarttext">
          Ronda - Puente Nuevo &amp; el Tajo
         </div>
         <img alt="Webcam Ronda - Puente Nuevo &amp; el Tajo" class="imagestart" src="thumbs/cams/ronda.jpg" title="Webcam Ronda - Puente Nuevo &amp; el Tajo"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="tarifa.php">
         <div class="camstarttext">
          Tarifa (Cádiz) - Playa de Los Lances
         </div>
         <img alt="Webcam Tarifa (Cádiz) - Playa de Los Lances" class="imagestart" src="thumbs/cams/tarifa.jpg" title="Webcam Tarifa (Cádiz) - Playa de Los Lances"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="tarifa-playa.php">
         <div class="camstarttext">
          Tarifa (Cádiz) - Playa de Los Lances II
         </div>
         <img alt="Webcam Tarifa (Cádiz) - Playa de Los Lances" class="imagestart" src="thumbs/cams/tarifasurfer.jpg" title="Webcam Tarifa (Cádiz) - Playa de Los Lances"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="granada-alhambra.php">
         <div class="camstarttext">
          Granada - La Alhambra
         </div>
         <img alt="Webcam Granada - La Alhrambra" class="imagestart" src="thumbs/cams/granada.jpg" title="Webcam Granada - La Alhrambra"/>
         <div class="live">
         </div>
        </a>
       </div>
       <!--<div class="camstart"><a href="almeria.php"> <div class="camstarttext">Vera (Almeria) - Aeroclub Vera</div> <img src="thumbs/cams/vera.jpg" class="imagestart" alt="Webcam Vera (Almeria) - Aeroclub Vera" title="Webcam Vera (Almeria) - Aeroclub Vera" /> <div class="livebild"></div></a></div>   -->
      </div>
      <div class="camsortindex4">
       Live - webcams in Spain
      </div>
      <div class="cams1">
       <div class="camstart">
        <a href="valencia-puerto.php">
         <div class="camstarttext">
          Port of Valencia
         </div>
         <img alt="Webcam Port of Valencia" class="imagestart" src="thumbs/cams/valenciapuerto.jpg" title="Webcam Port of Valencia"/>
         <div class="live">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="valencia.php">
         <div class="camstarttext">
          Valencia -  La Malvarrossa Beach
         </div>
         <img alt="Webcam Valencia - Beach de la Malvarrossa" class="imagestart" src="thumbs/cams/valenciaplaya.jpg" title="Webcam Valencia - Beach de la Malvarrossa"/>
         <div class="live">
         </div>
        </a>
       </div>
      </div>
      <div class="camsortindex4">
       Guest - webcams in Spain
      </div>
      <div class="cams1">
       <div class="camstart">
        <a href="torremolinos-carihuela.php">
         <div class="camstarttext">
          Torremolinos (Málaga) - Playa La Carihuela
         </div>
         <img alt="Webcam Torremolinos (Málaga) - Playa La Carihuela" class="imagestart" src="thumbs/cams/carihuela.jpg" title="Webcam Torremolinos (Málaga) - Playa La Carihuela"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="sierranevada-pradollano.php">
         <div class="camstarttext">
          Sierra Nevada (Granada) - Pradollano
         </div>
         <img alt="Webcam Sierra Nevada (Granada) - Pradollano" class="imagestart" src="thumbs/cams/campradollano.jpg" title="Webcam Sierra Nevada (Granada) - Pradollano"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <!--  <div class="camstart"><a href="sierranevada-borreguiles.php"> <div class="camstarttext">Sierra Nevada (Granada) - Borreguiles</div> <img src="thumbs/cams/camborreguiles.jpg" class="imagestart" alt="Webcam Sierra Nevada (Granada) - Borreguiles" title="Webcam Sierra Nevada (Granada) - Borreguiles" /> <div class="livebild"></div></a></div>   
    
    <div class="camstart"><a href="sierranevada-borreguilestaller.php"> <div class="camstarttext">Sierra Nevada (Granada) - Borreguiles</div> <img src="thumbs/cams/camborreguilestaller.jpg" class="imagestart" alt="Webcam Sierra Nevada (Granada) - Borreguiles" title="Webcam Sierra Nevada (Granada) - Borreguiles" /> <div class="livebild"></div></a></div>   
   
   
    <div class="camstart"><a href="sierranevada-montebajo.php"> <div class="camstarttext">Sierra Nevada (Granada) - Montebajo</div> <img src="thumbs/cams/cammontebajo.jpg" class="imagestart" alt="Webcam Sierra Nevada (Granada) - Montebajo" title="Webcam Sierra Nevada (Granada) - Montebajo" /> <div class="livebild"></div></a></div>   -->
       <div class="camstart">
        <a href="galicia-corrubedo.php">
         <div class="camstarttext">
          A Coruña - Galicia - Corrubedo
         </div>
         <img alt="Webcam A Coruña - Galicia - Corrubedo" class="imagestart" src="thumbs/cams/camcorrubedo.jpg" title="Webcam A Coruña - Galicia - Corrubedo"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="galicia-baiona.php">
         <div class="camstarttext">
          Pontevedra - Galicia - Baiona
         </div>
         <img alt="Webcam Pontevedra - Galicia - Baiona" class="imagestart" src="thumbs/cams/cambaiona.jpg" title="Webcam Pontevedra - Galicia - Baiona"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="galicia-cies-rodas.php">
         <div class="camstarttext">
          Pontevedra - Galicia - Cíes-Rodas
         </div>
         <img alt="Webcam Pontevedra - Galicia - Cíes-Rodas" class="imagestart" src="thumbs/cams/camciesrodas.jpg" title="Webcam Pontevedra - Galicia - Cíes-Rodas"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="benicassim.php">
         <div class="camstarttext">
          Benicàssim - Castellón - Playa Voramar
         </div>
         <img alt="Webcam Benicàssim - Castellón - Playa Voramar" class="imagestart" src="thumbs/cams/benicassim1.jpg" title="Webcam Benicàssim - Castellón - Playa Voramar"/>
         <div class="livebild">
         </div>
        </a>
       </div>
       <div class="camstart">
        <a href="benicassim-playa.php">
         <div class="camstarttext">
          Benicàssim - Castellón - Playa Voramar
         </div>
         <img alt="Webcam Benicàssim - Castellón - Playa Voramar" class="imagestart" src="thumbs/cams/benicassim2.jpg" title="Webcam Benicàssim - Castellón - Playa Voramar"/>
         <div class="livebild">
         </div>
        </a>
       </div>
      </div>
      <div style="clear: both;">
      </div>
      <a href="https://meteo365.es/livecams/">
       <div class="backwebcams">
        &lt;&lt; LiveCams start page
       </div>
      </a>
      <div style="clear: both;">
      </div>
     </div>
     <script>
      //  var nods = document.getElementsByClassName('imagestart');
//for (var i = 0; i < nods.length; i++)
//{
//    nods[i].attributes['src'].value += "?t=" + Math.random();
//}
     </script>
    </div>
   </div>
   <footer>
    <div id="footup">
     <div id="fotterleft">
      <span id="seguir">
       Follow us:
      </span>
      <a href="https://www.facebook.com/spainweather.es/" target="_blank">
       <span id="facebook">
       </span>
      </a>
      <a href="https://twitter.com/meteo365_es" target="_blank">
       <div id="twitter">
        <svg class="bi bi-twitter-x" fill="currentColor" height="16" viewbox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
         <path d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865z">
         </path>
        </svg>
       </div>
      </a>
     </div>
     <div id="fotterright">
      <span id="contact">
       <a href="https://meteo365.es/" target="_blank">
        Interactive weather forecast
       </a>
      </span>
      <span id="contact">
       <a href="https://meteo365.es/livecams/" target="_blank">
        LiveCams
       </a>
      </span>
      <span id="contact">
       <a href="https://meteo365.es/earthquakes/" target="_blank">
        Earthquakes
       </a>
      </span>
      <span id="contact">
       <a href="https://meteo365.es/timezones/" target="_blank">
        TimeZones
       </a>
      </span>
     </div>
    </div>
    <div id="footbottom">
     <span id="contact2">
      <a href="https://meteo365.es/cams/" target="_blank">
       Become a premium partner of meteo365.es
      </a>
     </span>
     <span id="contact2">
      <a href="https://meteo365.es/howto.php" target="_blank">
       Contact
      </a>
     </span>
     <span id="contact2">
      <a href="https://mawe-mediaonline.es/aviso_legal.php" target="_blank">
       Imprint
      </a>
     </span>
     <span id="contact2">
      © meteo365.es, mawe mediaonline slu 2025
     </span>
    </div>
   </footer>
  </div>
  <script src="jsneu/camswerbung.js?t=1752673364">
  </script>
  <script>
   setTimeout(function() { if($('.showads').is(':visible')){  } else { $('#soistes').load('adblocker_en.php'); } }, 500);
  </script>
  <script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-153770453-1">
  </script>
  <script>
   window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date());gtag('config', 'UA-153770453-1');
  </script>
 </body>
</html>
<script>
 function setCookie(cname, cvalue, exdays) {
  var d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  var expires = "expires="+d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/" + "; Secure";
}
var meteo = "en";
setCookie("meteo365es", meteo, 365);
</script>
