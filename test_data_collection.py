#!/usr/bin/env python3
"""
Test script for data collection functionality.
"""

import sys
sys.path.append('src')

from surf_malaga.data_collection import (
    collect_beach_cam_data,
    collect_environmental_data,
    collect_tide_data,
    save_collection_data
)
from datetime import datetime
import json

def test_beach_cam():
    print("Testing beach cam data collection...")
    try:
        data = collect_beach_cam_data()
        # Extract image data separately to avoid JSON serialization issues
        image_data = data.pop('_image_data', None)
        print(f"Beach cam data: {json.dumps(data, indent=2)}")
        if image_data:
            print(f"Image data size: {len(image_data)} bytes")
            # Put image data back for saving
            data['_image_data'] = image_data
        return data
    except Exception as e:
        print(f"Error in beach cam collection: {e}")
        return None

def test_environmental():
    print("\nTesting environmental data collection...")
    try:
        data = collect_environmental_data()
        print(f"Environmental data: {json.dumps(data, indent=2)}")
        return data
    except Exception as e:
        print(f"Error in environmental collection: {e}")
        return None

def test_tide():
    print("\nTesting tide data collection...")
    try:
        data = collect_tide_data()
        print(f"Tide data: {json.dumps(data, indent=2)}")
        return data
    except Exception as e:
        print(f"Error in tide collection: {e}")
        return None

def test_full_collection():
    print("\n" + "="*50)
    print("FULL DATA COLLECTION TEST")
    print("="*50)
    
    timestamp = datetime.now()
    print(f"Collection timestamp: {timestamp}")
    
    # Collect all data
    cam_data = test_beach_cam()
    env_data = test_environmental()
    tide_data = test_tide()
    
    # Combine data
    combined_data = {
        'timestamp': timestamp.isoformat(),
        'environmental': env_data,
        'tide': tide_data,
        'beach_cam': {
            'image_url': cam_data.get('image_url') if cam_data else None,
            'image_saved': cam_data.get('_image_data') is not None if cam_data else False,
            'image_size_bytes': cam_data.get('image_size_bytes') if cam_data else None
        }
    }
    
    # Save data
    try:
        saved_paths = save_collection_data(
            combined_data,
            cam_data.get('_image_data') if cam_data else None,
            timestamp
        )
        print(f"\nData saved to: {saved_paths}")
        
        # Verify saved data
        if 'json_path' in saved_paths:
            with open(saved_paths['json_path'], 'r') as f:
                loaded_data = json.load(f)
            print(f"\nVerified saved JSON data:")
            print(json.dumps(loaded_data, indent=2))
            
    except Exception as e:
        print(f"Error saving data: {e}")

if __name__ == "__main__":
    test_full_collection()
