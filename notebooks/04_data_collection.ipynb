{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Real-time Data Collection\n", "\n", "This notebook implements the data collection functionality for:\n", "- Beach cam images from meteo365.es\n", "- Environmental data (wind, temperature, etc.) from meteo365.es\n", "- Tide data from tideking.com\n", "\n", "The collected data is saved to the `data/external/` directory organized by date."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append('../src')\n", "\n", "import requests\n", "import json\n", "from datetime import datetime\n", "from pathlib import Path\n", "import pandas as pd\n", "from bs4 import BeautifulSoup\n", "import re\n", "from surf_malaga.data_collection import (\n", "    collect_beach_cam_data,\n", "    collect_environmental_data,\n", "    collect_tide_data,\n", "    save_collection_data,\n", "    interpolate_tide_height\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Individual Collection Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test beach cam data collection\n", "print(\"Testing beach cam data collection...\")\n", "cam_data = collect_beach_cam_data()\n", "print(f\"Beach cam data: {cam_data}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test environmental data collection\n", "print(\"Testing environmental data collection...\")\n", "env_data = collect_environmental_data()\n", "print(f\"Environmental data: {env_data}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test tide data collection\n", "print(\"Testing tide data collection...\")\n", "tide_data = collect_tide_data()\n", "print(f\"Tide data: {tide_data}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Full Data Collection Run"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Collect all data and save to disk\n", "print(\"Starting full data collection...\")\n", "\n", "# Get current timestamp\n", "timestamp = datetime.now()\n", "print(f\"Collection timestamp: {timestamp}\")\n", "\n", "# Collect all data\n", "cam_data = collect_beach_cam_data()\n", "env_data = collect_environmental_data()\n", "tide_data = collect_tide_data()\n", "\n", "# Combine environmental and tide data\n", "combined_data = {\n", "    'timestamp': timestamp.isoformat(),\n", "    'environmental': env_data,\n", "    'tide': tide_data,\n", "    'beach_cam': {\n", "        'image_url': cam_data.get('image_url'),\n", "        'image_saved': False,  # Will be updated after saving\n", "        'image_size_bytes': cam_data.get('image_size_bytes'),\n", "        'camera_location': cam_data.get('camera_location'),\n", "        'content_type': cam_data.get('content_type')\n", "    }\n", "}\n", "\n", "# Save data\n", "saved_paths = save_collection_data(combined_data, cam_data.get('_image_data'), timestamp)\n", "\n", "# Update the image_saved property with the filename\n", "if 'image_filename' in saved_paths:\n", "    combined_data['beach_cam']['image_saved'] = saved_paths['image_filename']\n", "\n", "print(f\"Data saved to: {saved_paths}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify Saved Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and display the saved JSON data\n", "if 'json_path' in saved_paths:\n", "    with open(saved_paths['json_path'], 'r') as f:\n", "        loaded_data = json.load(f)\n", "    print(\"Saved JSON data:\")\n", "    print(json.dumps(loaded_data, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if image was saved\n", "if 'image_path' in saved_paths:\n", "    image_path = Path(saved_paths['image_path'])\n", "    if image_path.exists():\n", "        print(f\"Image saved successfully: {image_path}\")\n", "        print(f\"Image size: {image_path.stat().st_size} bytes\")\n", "    else:\n", "        print(f\"Image file not found: {image_path}\")\n", "else:\n", "    print(\"No image path returned\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}