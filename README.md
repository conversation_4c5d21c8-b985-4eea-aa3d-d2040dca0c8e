# Surf Malaga Data Science Project

This project is a simple experiment, taking historical wind and wave data in the form of windguru page HTML, and seeing if we can build a model that predicts surf the wavesthe waves. The input is a raw save of the Windguru page.

## Plan

1. Extract the data from the HTML
2. Clean the data
3. Explore model options:
    1. DBSCAN / HDBSCAN - Surfable days form rare clusters
    2. Autoencoder / VAE - Surfable days = anomaly
    3. Isolation Forest - Data is tabular + rare events
    4. Contrastive Learning - You want surf-specific embeddings
    5. LSTM Sequence Embedding - Capturing temporal build-up
4. Train a model that can self-supervise to learn to predict surfability
5. Periodically scrape the beach cam and associated environmental data to build an image-data pair dataset
   - <https://meteo365.es/livecams/malaga.php>
   - <https://tideking.com/Spain/Andalusia/Provincia-de-Malaga/Playa-de-la-Malagueta>
6. Use trained environmental model as label generator for image-data pairs
7. Train an autoencoder classifier on the image-data pairs to predict surfability
8. Update scraper to infer surfability from beach cam image
9. Add an email/SMS/other notification system

## Project Structure

```text
surf_malaga/
├── README.md              # Project documentation
├── pyproject.toml          # Project configuration and dependencies
├── requirements.lock       # Locked dependencies
├── requirements-dev.lock   # Locked dev dependencies
├── .venv/                  # Virtual environment (managed by Rye)
├── notebooks/              # Jupyter notebooks for analysis
├── data/                   # Data files
│   ├── raw/               # Raw, immutable data
│   ├── processed/         # Cleaned and processed data
│   └── external/          # External data sources
├── src/                   # Source code modules
│   └── surf_malaga/       # Main package
├── tests/                 # Test files
├── docs/                  # Documentation
└── files/                 # Project files (existing)
```

## Setup

This project uses [Rye](https://rye-up.com/) for Python package management.

### Prerequisites

- Python 3.12
- Rye package manager

### Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   rye sync
   ```

### Running Jupyter

To start Jupyter Lab:

```bash
rye run jupyter lab
```

To start Jupyter Notebook:

```bash
rye run jupyter notebook
```

## Usage

1. Place raw data files in `data/raw/`
2. Create analysis notebooks in `notebooks/`
3. Develop reusable code in `src/surf_malaga/`
4. Write tests in `tests/`

## Data

The project includes surf condition data for Malaga extracted from Windguru HTML files and processed into structured datasets.

### Raw Data

- `data/raw/Windguru_El_Palo_Wave_2h_2023-07-01_2025-07-11.html` - Wave data HTML
- `data/raw/Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html` - Wind data HTML
- `data/raw/tides/Sotogrande_YYYY_N.pdf` - Tide data PDFs from Sotogrande (nearby location)
- Screenshots of conditions

### Processed Data Files

The `data/processed/` directory contains cleaned and structured datasets extracted from the raw HTML files:

#### Individual Dataset Files

- **`wave_data_YYYYMMDD_HHMMSS.{csv,parquet}`** - Wave-only data with columns:
  - `datetime` - Full timestamp (YYYY-MM-DD HH:MM:SS)
  - `date` - Date component (YYYY-MM-DD)
  - `time` - Time interval (00h, 02h, 04h, etc.)
  - `wave_height_m` - Wave height in meters
  - `wave_period_s` - Wave period in seconds
  - `wave_direction_to_deg` - Direction waves are traveling TO (0-360°)

- **`wind_data_YYYYMMDD_HHMMSS.{csv,parquet}`** - Wind-only data with columns:
  - `datetime` - Full timestamp (YYYY-MM-DD HH:MM:SS)
  - `date` - Date component (YYYY-MM-DD)
  - `time` - Time interval (00h, 02h, 04h, etc.)
  - `wind_speed_knots` - Wind speed in knots
  - `wind_direction_from_deg` - Direction wind is blowing FROM (0-360°)
  - `temperature_c` - Temperature in Celsius

- **`tide_data_YYYYMMDD_HHMMSS.{csv,parquet}`** - Tide-only data with columns:
  - `datetime` - Full timestamp (YYYY-MM-DD HH:MM:SS)
  - `date` - Date component (YYYY-MM-DD)
  - `time` - Time interval (00h, 02h, 04h, etc.)
  - `tide_height_m` - Tide height in meters (interpolated from Sotogrande data)

#### Combined Dataset Files

- **`malaga_surf_data_YYYYMMDD_HHMMSS.{csv,parquet}`** - Complete surf dataset combining wind and wave data with 100% data completeness. Contains all columns from both wave and wind datasets.

#### Latest Data Access

- **`malaga_surf_data_latest.{csv,parquet}`** - Symlinks to the most recently extracted combined dataset for easy access
- **`tide_data_latest.{csv,parquet}`** - Symlinks to the most recently extracted tide dataset for easy access

### Data Characteristics

- **Time Resolution**: 2-hour intervals (00h, 02h, 04h, 06h, 08h, 10h, 12h, 14h, 16h, 18h, 20h, 22h)
- **Date Range**: July 1, 2023 to July 11, 2025 (historical + forecast data)
- **Data Completeness**: 100% - missing values are filled with sensible defaults to preserve time series integrity
- **File Formats**: Both CSV (for compatibility) and Parquet (recommended for analysis due to better performance and type preservation)

### Data Quality Notes

- Missing wave directions default to 225° (SW, predominant Mediterranean direction)
- Missing wind directions default to 180° (S, common thermal wind direction)
- Missing values are handled with interpolation and sensible defaults rather than removal to maintain temporal continuity
- All direction values are normalized to 0-360° range
- Wind directions represent the direction wind is blowing FROM (meteorological convention)
- Wave directions represent the direction waves are traveling TO
- Tide data is extracted from Sotogrande PDFs (nearby location) and interpolated using cubic spline to 2-hour intervals
- Tide heights are in meters relative to mean sea level

## Contributing

1. Create feature branches for new analysis
2. Write tests for any reusable functions
3. Document your analysis in notebooks
4. Update this README as needed
