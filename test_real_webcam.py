#!/usr/bin/env python3
"""
Test the real webcam URL.
"""

import requests

USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

def test_webcam_url():
    session = requests.Session()
    session.headers.update({
        'User-Agent': USER_AGENT,
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'https://meteo365.es/livecams/malaga.php',
    })
    
    webcam_url = "https://webcam.meteo365.es/livecams/playa/current.webp"
    
    try:
        print(f"Testing webcam URL: {webcam_url}")
        response = session.get(webcam_url, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Content-Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if content_type.startswith('image/'):
                print("✓ Successfully retrieved live webcam image!")
                
                # Save the image
                with open('live_webcam.webp', 'wb') as f:
                    f.write(response.content)
                print("Saved to live_webcam.webp")
                
                return True
            else:
                print(f"❌ Not an image: {content_type}")
                print(f"Response content: {response.text[:200]}")
        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    test_webcam_url()
